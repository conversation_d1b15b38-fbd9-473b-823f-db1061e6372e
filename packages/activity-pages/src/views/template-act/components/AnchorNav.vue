<template>
  <!-- 占位元素，用于防止滚动吸附时页面跳动 -->
  <!-- <div v-if="isFixed" class="anchor-placeholder"></div> -->
  <div class="anchor-wrap">
    <div
      ref="anchorRef"
      class="anchor-nav"
      :class="`${uiType} ${isFixed ? 'fixed-top' : ''} ${navList.length > 4 ? 'is-over-4' : ''}`"
    >
      <div class="nav-item-wrap" ref="navItemWrapRef">
        <div
          class="nav-item"
          v-for="(item, index) in navList"
          :key="item.id"
          @click="handleClick(item.id, item.targetTop)"
          :class="{ actived: selectedIndex === index }"
        >
          <p v-for="text in item.texts" class="nav-item-title">{{ text }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, watchEffect } from 'vue'
import { ref, onMounted, onUnmounted, toRaw } from 'vue'
import NumberSpaceAroundText from './NumberSpaceAroundText.vue'
import { focusCore, focusStore, focusServices, Mask, focusUtils, DialogMask } from '@focus/render'
const { throttle } = focusUtils
const props = defineProps({
  uiType: {
    type: String,
    default: 'type1',
  },
  productList: {
    type: Array,
    default: () => [],
  },
  productCardTopList: {
    type: Array,
    default: () => [],
  },
})
const selectedIndex = ref(0)
const navItemWrapRef = ref<HTMLElement | null>(null)
const selfHeight = computed(() => {
  return anchorRef.value?.clientHeight || 0
})

const navList = computed(() => {
  return props.productList.map((item: any, index) => {
    // console.log('🚀 ~ item:', item)
    const { requiredAmount, rewardPoints, ruleType, requiredTimes } = item
    let firstStr = ''
    const aipWays: any = {
      EVERY_DAY: '每天',
      EVERY_WEEK: '每周',
      EVERY_TWO_WEEK: '每双周',
      EVERY_MONTH: '每月',
    }
    if (requiredAmount.toString().indexOf('0000') > -1) {
      firstStr = `每${requiredAmount.toString().replace('0000', '万')}元`
    } else {
      firstStr = `每${requiredAmount.toString()}元`
    }

    if (ruleType === 'AIP') {
      firstStr = `连续定投${requiredTimes}期`
    }

    return {
      texts: [firstStr, `奖${rewardPoints}积分`],
      id: index,
      targetTop: props.productCardTopList[index] || 0,
    }
  })
})

// 引用组件根元素
const anchorRef = ref<HTMLElement | null>(null)
// 跟踪是否固定定位
const isFixed = ref(false)
// 记录初始偏移量
let initialOffset = 0
const isScrolled = ref(false)
const scrollTimer = ref<any>(null)
// 计算固定定位状态
const handleScroll = () => {
  isScrolled.value = true
  if (scrollTimer.value) {
    clearTimeout(scrollTimer.value)
  }
  scrollTimer.value = setTimeout(() => {
    isScrolled.value = false
  }, 50)
  if (!anchorRef.value) return
  const scrollPosition = window.scrollY
  // console.log('🚀 ~ scrollPosition:', scrollPosition)
  // 当滚动超过组件初始位置时固定
  isFixed.value = scrollPosition >= initialOffset
}

watchEffect(() => {
  if (!isScrolled.value) {
    const index = findClosestIndex(toRaw(props.productCardTopList), window.scrollY)
    // console.log('🚀 ~ handleScroll ~ index:', index)
    selectedIndex.value = index > 0 ? index : 0
    // console.log('🚀 ~  selectedIndex.value:', selectedIndex.value)
  }
  if (selectedIndex.value > -1 && anchorRef.value) {
    // 滚动到最左边
    if (selectedIndex.value === 0) {
      anchorRef.value.scrollLeft = 0
    }
    if (selectedIndex.value === navList.value.length - 1) {
      anchorRef.value.scrollLeft = anchorRef.value.scrollWidth - anchorRef.value.clientWidth
    }
  }
})

function findClosestIndex(sortedArr: number[] | any, target: number) {
  // console.log('🚀 ~ findClosestIndex ~ target:', target)
  // console.log('🚀 ~ findClosestIndex ~ sortedArr:', sortedArr)
  let left = 0
  let right = sortedArr.length - 1

  // 处理空数组或单元素数组
  if (sortedArr.length === 0) return -1
  if (sortedArr.length === 1) return 0

  // 二分查找定位插入位置
  while (left < right) {
    const mid = Math.floor((left + right) / 2)
    if (sortedArr[mid] < target) {
      left = mid + 1
    } else {
      right = mid
    }
  }

  // 此时 left 是插入位置，需比较 left 和 left-1 的元素
  const candidate = left === 0 ? left : left - 1
  const next = left === sortedArr.length ? left - 1 : left

  // 比较距离并返回最接近的索引
  return Math.abs(sortedArr[candidate] - target) <= Math.abs(sortedArr[next] - target) ? candidate : next
}

function handleClick(id: number, targetTop: number) {
  // if (selectedIndex.value === id) {
  //   return
  // }
  console.log('🚀 ~ handleClick ~ id:', id)
  selectedIndex.value = id
  nextTick(() => {
    doAnimateScroll(targetTop)
  })
}

function doAnimateScroll(targetTop: number) {
  // console.log('🚀 ~ doAnimateScroll ~ targetTop:', targetTop)
  // const targetPosition = anchorRef.value.offsetTop
  // console.log('🚀 ~ doAnimateScroll ~ selfHeight.value:', selfHeight.value)
  window.scrollTo({
    top: targetTop - selfHeight.value,
    behavior: 'smooth',
  })
}

// 挂载时设置初始位置和添加滚动监听
onMounted(() => {
  if (anchorRef.value) {
    initialOffset = anchorRef.value.offsetTop
  }
  window.addEventListener('scroll', handleScroll)
})

// 卸载时移除滚动监听
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped lang="scss">
.type1 {
  --background-1: linear-gradient(0deg, #deefff 0%, #e7f3ff 100%);
  --text-color-1: #4b5985;
  --background-2: linear-gradient(181deg, #bce1ff 0.88%, #def0ff 118.44%);
  --border-color-1: #b9dfff;
}

.type2 {
  --background-1: linear-gradient(180deg, var(--, #ffe9cd) 0%, #ffe3c0 100%);
  --text-color-1: #c45719;
  --background-2: linear-gradient(0deg, #ffe3c0 0%, #ffd59d 100%);
}

.type3 {
  --background-1: linear-gradient(0deg, #ffe8e3 0%, #ffeded 100%);
  --text-color-1: #911735;
  --background-2: linear-gradient(0deg, #ffe8e3 0%, #ffd3d7 100%);
}

.anchor-nav {
  transition: all 0.3s ease;
  align-items: center;
  width: 100%;
  height: 100px;
  box-sizing: border-box;
  background: var(--background-1);
  overflow: hidden;
  &::-webkit-scrollbar {
    background: transparent;
    height: 0;
  }
  .nav-item-wrap {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &.is-over-4 {
    overflow-x: scroll;
    .nav-item-wrap {
      width: 890px;
    }
  }
}

/* 占位元素样式 */
.anchor-wrap {
  width: 100%;
  height: 100px;
  box-sizing: border-box;
}

.nav-item {
  flex: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  position: relative;
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    width: 1px;
    height: 60px;
    background: var(--border-color-1);
    transform: translateY(-50%);
  }
  &:last-child {
    &::after {
      display: none;
    }
  }
  .nav-item-title {
    font-size: 22px;
    line-height: 30px;
    letter-spacing: 1px;
    color: var(--text-color-1);
  }
  &.actived {
    background: var(--background-2);
    .nav-item-title {
      font-weight: 600;
    }
  }
}

/* 固定定位样式 */
.fixed-top {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
</style>
