<template>
  <p class="number-space-around-text" v-html="parsedText"></p>
</template>

<script setup lang="ts">
//   <!-- 将文本中的数字前后添加4px空格 -->

import { computed } from '@vue/reactivity'

const props = defineProps({
  text: {
    type: String,
    default: '',
  },
  textSize: {
    type: Number,
    default: 20,
  },
})

const parsedText = computed(() => {
  // console.log('🚀 ~....... props.text:', props.text)
  const regex = /([+-]?(?:\d{1,3}(?:,\d{3})+|\d+)(?:\.\d+)?%?(?:[eE][-+]?\d+)?)/g
  const fontSize = (props.textSize / 750) * 100 + 'vw'
  const result = props.text.replace(regex, '<span class="number-text" style="font-size: ' + fontSize + ';">$1</span>')
  // console.log('🚀 ~....... result:', result)

  return result
})
</script>

<style lang="scss">
.number-space-around-text {
  span.number-text {
    margin: 0 4px;
    color: #f24c3d;
  }
}
</style>
