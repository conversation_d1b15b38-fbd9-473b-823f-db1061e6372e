<template>
  <div class="prodcode-cards" ref="prodcodeCardsRef" v-if="codeDatas.length">
    <div class="prod-list">
      <div class="rule-card" v-if="ruleData.ruleDataType !== '6'">
        <div class="left">
          <NumberSpaceAroundText class="title" :text="ruleData.ruleTextTitle" :textSize="26"> </NumberSpaceAroundText>
          <p class="desc">{{ ruleData.ruleTextDesc }}</p>
        </div>
        <div class="right">
          <p class="point">{{ ruleData.rewardPoints }}<span>积分</span></p>
          <NumberSpaceAroundText class="bonus" :text="ruleData.rewardBonusText" :textSize="20"> </NumberSpaceAroundText>
        </div>
      </div>

      <div class="rule-card aip" v-if="ruleData.ruleDataType === '6'">
        <div class="left">
          <div class="row">
            <p class="p1">{{ ruleData.ruleTextTitle }}</p>
            <p class="p2">每只连续达标期数</p>
          </div>
          <div class="row">
            <p class="p1">≥{{ ruleData.requiredAmount }}元</p>
            <p class="p2">{{ ruleData.requiredTimes }}期</p>
          </div>
        </div>
        <div class="right">
          <p>每只</p>
          <p class="point">{{ ruleData.rewardPoints }}<span>积分</span></p>
        </div>
      </div>

      <div
        class="item"
        v-for="(item, index) in showData"
        :key="index"
        @click="jumpCode(item)"
        :class="{ 'is-end': item.isActEnd }"
      >
        <div class="title">
          <img :src="item.bankLogoUrl" alt="" class="icon" v-show="item.bankLogoUrl" />
          <p class="name">{{ item.productName }}</p>
        </div>
        <div class="middle-info">
          <p class="rate">
            {{ item.formateRateValue[0] }}<span>%</span
            >{{ item.formateRateValue[1] ? `- ${item.formateRateValue[1]}` : ''
            }}<span v-if="item.formateRateValue[1]">%</span>
          </p>
          <p class="days" v-if="item.productPeriod">{{ item.productPeriod }}</p>
          <div
            class="btn"
            :class="{ 'is-saleout': item.isSaleOut }"
            @click="jumpCode(item)"
            v-focuslog.click="{
              definedValue: `fundCoupon-btn-go`,
              definedInfo: {
                prodCode: item.productCode,
              },
            }"
          >
            {{ item.btnText }}
          </div>
        </div>
        <div class="bottom-info">
          <p>{{ item.rateDesc }}</p>
          <!-- <p>{{ item.rateText || '再买xx元奖励xx积分' }}</p> -->
          <!-- <p>{{ item.establishDate }}成立</p> -->
        </div>
        <div class="sale-tip">{{ item.recommendation }}</div>
      </div>
    </div>
    <div
      class="btn-more"
      @click="isShowMore = !isShowMore"
      :class="{ 'is-showmore': isShowMore }"
      v-show="props.codeDatas.length > 3"
    >
      {{ isShowMore ? '收起' : '更多指定产品' }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { focusServices } from '@focus/render'
import { computed, onMounted, ref } from 'vue'
import type { TProductCardItem } from '../service/service'
import NumberSpaceAroundText from './NumberSpaceAroundText.vue'
const { jumpService, activityService } = focusServices
const prodcodeCardsRef: any = ref(null)
const emit = defineEmits(['onTopIsReady'])
const props = defineProps<{
  clickAddingAid: string
  ruleData: {
    ruleDataType: string
    ruleTextTitle: string
    ruleTextDesc: string
    rewardPoints: string
    rewardBonusText: string
    requiredAmount: string
    requiredTimes: string
  }
  codeDatas: Array<
    TProductCardItem & {
      btnText: string
      isActEnd: boolean
      days: string
      saleTip: string
      setupTime: string
      isSaleOut: boolean
      formateRateValue: Array<string>
    }
  >
  rateStatisicsDate: string
}>()

const isShowMore = ref(false)

const ruleData = computed(() => {
  return (
    props.ruleData || {
      type: '1',
      costTitle: '单只产品',
      costMoney: '10000',
      desc: '需活动期间新买入且持有至活动结束',
      rewardPoint: '333',
    }
  )
})

const rateStatisicsDate = computed(() => {
  return props.rateStatisicsDate || ''
})

const showData = computed(() => {
  const datas = props.codeDatas || []
  console.log('🚀  ~ prod datas:', datas)
  return isShowMore.value ? datas : datas.slice(0, 3)
})

function jumpCode(item: any) {
  console.log('🚀 ~ jumpCode ~ item:', item)
  if (item.isSaleOut) {
    return
  }
  if (props.clickAddingAid) {
    activityService.setWrInfo(props.clickAddingAid).then(() => {
      jumpService.jump({
        path: item.productCode,
      })
    })
    return
  }
  jumpService.jump({
    path: item.productCode,
  })
}

console.log('.....prodcode')

onMounted(() => {
  console.log('🚀 ~ prodcodeCardsRef:', prodcodeCardsRef.value)
  if (prodcodeCardsRef.value) {
    console.log('🚀 ~ prodcodeCardsRef.value:', prodcodeCardsRef.value)
    const rect = prodcodeCardsRef.value.getBoundingClientRect()
    console.log('🚀 ~ rect:', rect)
    const { top } = rect
    emit('onTopIsReady', top)
  }
})
</script>

<style lang="scss" scoped>
.prodcode-cards {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 20px;
  padding: 20px 15px;
  height: auto;
  width: 702px;
  margin: 0 auto;
  margin-bottom: 32px;
  .prod-list {
    background-color: #fff;
    border-radius: 20px;
    margin: 0 auto;
    width: 670px;
    overflow: hidden;

    .rule-card {
      width: 670px;
      margin: 0 auto;
      height: 150px;
      background: url('../img/bg-rulecard-1.png') no-repeat;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 400;
      font-size: 24px;
      line-height: 36px;
      padding: 1px;
      box-sizing: border-box;
      overflow: hidden;

      .left {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        flex: 1;
        height: 100%;
        position: relative;
        text-align: left;
        color: #9b431e;
        padding-left: 24px;
        .title {
          padding-left: 20px;
          font-size: 24px;
          font-weight: 400;
          line-height: 36px; /* 150% */
          width: 100%;
          box-sizing: border-box;
        }
        .desc {
          padding-left: 20px;
          font-size: 20px;
          line-height: 30px; /* 150% */
          opacity: 0.5;
          width: 100%;
          box-sizing: border-box;
          margin-top: 10px;
        }
      }
      .right {
        border-radius: 0 15px 15px 0;
        display: flex;
        align-items: flex-start;
        justify-content: center;
        flex-direction: column;
        width: 252px;
        height: 100%;
        box-sizing: border-box;
        padding-left: 40px;
        .point {
          font-size: 56px;
          color: var(--prodcard-color-4);
          line-height: 56px;
          span {
            font-size: 24px;
            margin-left: 4px;
          }
        }
        .bonus {
          color: #a55121;
          font-size: 20px;
          line-height: 20px;
        }
      }

      &.aip {
        .left {
          padding: 0 35px;
        }
        .right {
          color: #9b431e;
        }

        .row {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          &:last-child {
            margin-top: 20px;
          }
          .p2 {
            text-align: left;
            width: 192px;
          }
        }
      }
    }

    .item {
      width: 100%;
      box-sizing: border-box;
      padding: 24px;
      position: relative;
      overflow: hidden;
      &.is-end {
        &::before {
          content: '';
          position: absolute;
          right: 0;
          top: 0;
          width: 110px;
          height: 56px;
          background-color: red;
        }
      }
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 622px;
        height: 1px;
        background: #f0f0f0;
      }
      &:last-child {
        &::after {
          display: none;
        }
      }
      .title {
        display: flex;
        align-items: centern;
        justify-content: flex-start;
        width: 100%;
        padding: 0 8px;
        .icon {
          width: 32px;
          height: 32px;
          margin-right: 7px;
        }
        .name {
          font-size: 24px;
          color: var(--prodcard-color-1);
          line-height: 36px;
          font-weight: 600;
          width: 100%;
          text-align: left;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-box-orient: vertical;
        }
      }
      .middle-info {
        padding: 0 8px;
        padding-top: 8px;
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        .rate {
          font-size: 36px;
          color: var(--prodcard-color-4);
          line-height: 36px;
          font-weight: 500;
          span {
            font-size: 30px;
            font-weight: bold;
          }
        }
        .days {
          font-size: 24px;
          color: #61041b;
          line-height: 24px;
        }
        .btn {
          width: 136px;
          height: 54px;
          background: var(--prodcard-color-4);
          border-radius: 32px;
          color: #fff;
          line-height: 54px;
          text-align: center;
          margin-right: 8px;
          font-size: 24px;
          align-self: center;
          &:active {
            opacity: 0.7;
          }
          &.is-saleout {
            opacity: 0.5;
            &:active {
              opacity: 0.5;
            }
          }
        }
      }
      .bottom-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 8px;
        padding-top: 16px;
        p {
          font-size: 20px;
          color: var(--prodcard-color-2);
          line-height: 30px;
        }
      }
      .sale-tip {
        width: 100%;
        height: 44px;
        background: linear-gradient(to right, rgba(255, 243, 239, 1), rgba(255, 243, 239, 0));
        border-radius: 15px;
        font-size: 18px;
        line-height: 18px;
        color: var(--prodcard-color-3);
        text-align: left;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-top: 16px;
        &::before {
          content: ' ';
          display: inline-block;
          width: 21px;
          height: 18px;
          background: url('../img/icon-tip.png') no-repeat;
          background-size: 100% 100%;
          margin-right: 5px;
          margin-left: 16px;
        }
      }
    }
  }

  .btn-more {
    width: 100%;
    height: 28px;
    line-height: 28px;
    padding-bottom: 10px;
    text-align: center;
    font-size: 20px;
    color: #456ce6;
    margin-top: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    &::after {
      content: '';
      display: inline-block;
      width: 24px;
      height: 24px;
      background: url('../img/icon-arrow-down.png');
      background-size: 100% 100%;
      margin-left: 8px;
    }
    &.is-showmore {
      &::after {
        transform: rotate(180deg);
      }
    }
  }
}
.rate-date {
  font-size: 20px;
  color: #b0818d;
  letter-spacing: 0;
  line-height: 30px;
  margin-top: 32px;
}
</style>
