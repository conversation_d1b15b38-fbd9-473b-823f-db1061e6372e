import { focusStore, focusServices, focusCore, focusUtils } from '@focus/render'
import dayjs from 'dayjs'

export interface TProductCardItem {
  bankLogoUrl?: string // 银行logo
  bankShortName?: string // 银行机构名称
  productCode?: string // 产品code
  productName?: string // 产品名称
  rateValue?: string // 收益率数值
  rateDesc?: string // 收益率描述
  productPeriod?: string // 产品周期
  recommendation?: string // 推荐语
  saleStatus?: string // 销售状态，01去购买；02已售罄；03已截售
  establishDate?: string // 成立日期
  rewardStatus?: string // 奖励期状态，01奖励期结束，02奖励期未结束
}

interface TTemplateData {
  templateType?: string // 模板类型：PRODUCT_BUY（产品购买）
  rewardRule?: {
    isCanSeeProduct?: string // 是否展示产品卡片：1-是，0-否
    earningsRateDate?: string // 收益率统计日期
    templateSubType?: string // 模板子类型：01-规模型-单只产品计算，02-规模型-跨产品合并计算，03-达标型-多次产品分别计算，04-达标型-任意一只达标奖励一次，05-达标型-跨产品合并只奖励一次，06-基金定投
    ruleType?: string // 规则类型：SINGLE_BUY-单次购买, CUMULATIVE_BUY-累计购买, AIP-基金定投
    buyAmountCalcWay?: string // 购买金额计算方式，0-申购，1-净申购
    allowRedeem?: string // 是否允许赎回：1-是，0-否
    multiProductCalcTogether?: string // 多产品是否合并计算：1-是，0-否 购买规模和购买达标用
    multiProductRewardOnce?: string // 多产品是否只贡献一次积分: 1-是，0-否,多产品是否合并计算取否时
    singleProductMultiPlanRewardOnce?: string // 单产品多计划是否只贡献一次积分: 1-是，0-否
    isBonus?: string // 是否加码：1-是，0-否
    bonusType?: string // 加码类型，加码奖励类型：PERCENTAGE-比例，ABSOLUTE-绝对值
    bonusGroupType?: string // 加码人群，CUST_GROUP-人群包, MEMBER_LEVEL-会员等级, ENTERPRISE_AUTH-企业认证, NEW_CUST 新户
    bonusPercentage?: number // 加码比例
    wealthRank?: string // 新财富+用户等级,1:白银,2:黄金,3:铂金,4:钻石
    rewardList?: {
      aipWay?: string // 定投方式，多选，逗号分隔，EVERY_DAY-每天，EVERY_WEEK-每周，EVERY_TWO_WEEK-每双周，EVERY_MONTH-每月
      requiredTimes?: number // 需要连续的定投次数
      requiredAmount?: number // 需要达到的金额（x元）
      rewardPoints?: number // 奖励积分值,奖励基础积分值+奖励加码积分值
      rewardBasePoints?: number // 奖励基础积分值
      rewardBonusPoints?: number // 奖励加码积分值
      productList?: TProductCardItem[] // 产品卡片list
    }[]
    productTypeDesc?: string //产品类型说明；1-只有理财子 2-只有基金产品 3-理财子&基金产品
  }
}

interface TPageConfig {
  // 模板类型：PRODUCT_BUY（产品购买）
  templateType?: string
  participateMethod?: string // 参与方式，1: 点击参与；2: 浏览参与；3: 无条件参与
  // 页面模板数据
  pageInfo?: {
    // 页面可访问开始时间
    accessStartTime?: string
    // 页面可访问结束时间
    accessEndTime?: string
    // 页面标题
    pageTitle?: string
    // 页面主题色调
    pageThemeColor?: string
    pageAccessDesc?: string // 页面访问时间判断 1: 未开始；2: 超过访问时间；3: 可访问
    // 分享信息
    shareInfo?: {
      // 右上角分享按钮状态，1-开启；2-关闭
      shareButton?: string
      // 分享标题
      shareTitle?: string
      // 分享内容
      shareContent?: string
      // 分享链接
      shareLink?: string
      // 分享图片URL
      shareImageUrl?: string
      // APP分享面板配置，1-分享面板[微信好友+朋友圈],2-分享面板[微信好友],3-分享面板[朋友圈],4-直接分享[微信好友]
      appSharePanel?: string
      // 分享海报URL
      sharePosterUrl?: string
    }

    // 头部信息
    topInfo?: {
      headImageUrl?: string // 头部图片URL
      activityCode?: string // 活动编号
      rewardWay?: string // 奖励规则
      bonusInfo?: {
        isBonus?: string // 是否加码，1-是，0-否
        bonusType?: 'PERCENTAGE' | 'ABSOLUTE' // 加码类型，加码奖励类型：PERCENTAGE-比例，ABSOLUTE-绝对值
        bonusGroupType?: 'CUST_GROUP' | 'MEMBER_LEVEL' | 'ENTERPRISE_AUTH' | 'NEW_CUST' // 加码人群，CUST_GROUP-人群包, MEMBER_LEVEL-会员等级, ENTERPRISE_AUTH-企业认证, NEW_CUST 新户
        bonusPercentage?: number // 加码比例（对应加码类型为PERCENTAGE）
        bonusAmount?: number // 加码绝对值（对应加码类型为ABSOLUTE）
        wealthRank?: string // 新财富+用户等级,1:白银,2:黄金,3:铂金,4:钻石
      }
    }
    // banner卡片list
    bannerCardList?: BannerCardItem[]
    // 积分兑好礼开关，1-开启；2-关闭
    pointsRedeem?: string
    // 选择理由开关，1-开启；2-关闭
    selectionReason?: string
    // 合作理财子开关，1-开启；2-关闭
    partnerWealth?: string
  }
}

interface BannerCardItem {
  // 展示图片URL
  displayImageUrl?: string
  // 展示开始时间
  displayStartTime?: string
  // 展示结束时间
  displayEndTime?: string
  // 1-不跳转；2-跳APP网页；3-跳微信网页；4-跳APP模块（最多添加5个额外参数）；5-跳APP产品页；6-跳小程序
  jumpType?: string
  // 跳转链接
  jumpLink?: string
  // 加密跳转链接
  jumpLinkSign?: string
  // 额外参数，Map形式，key value
  extraParams?: object
}

const cgis = {
  getTemplateInfo: {
    name: '查询活动模板信息-已开户',
    url: '/op-fmfront/activity/hj/fm/activityPageTemplateDataOpened',
  },
  getTemplateInfoNoAuth: {
    name: '查询活动模板信息-未开户',
    url: '/op-fmfront/activity/hj/fm/activityPageTemplateDataNotOpened',
  },
  getTemplateRewardInfo: {
    name: '查询活动模板奖励进度',
    url: '/op-fmfront/activity/hj/fm/activityTemplateRewardInfo',
  },
  getTemplateProductInfo: {
    name: '查询活动模板产品信息-已开户',
    url: '/op-fmfront/activity/hj/fm/activityPageRewardRuleDataOpened',
  },
  getTemplateProductInfoNoAuth: {
    name: '查询活动模板产品信息-未开户',
    url: '/op-fmfront/activity/hj/fm/activityPageRewardRuleDataNotOpened',
  },
  getPreviewTemplateData: {
    name: '查询活动页模板配置-预览数据',
    url: '/op-fmfront/activity/hj/fm/activityPageTemplatePreviewDataOpened',
  },
  getPreviewProductInfoData: {
    name: '查询活动页产品配置-预览数据',
    url: '/op-fmfront/activity/hj/fm/activityPageRewardRulePreviewDataOpened',
  },
  getActProgressData: {
    name: '查询活动进度信息',
    url: '/op-fmfront/activity/hj/fm/activityPagePredictRewardDataOpened',
  },
  getActProgressList: {
    name: '查询活动进度列表',
    url: '/op-fmfront/activity/hj/fm/activityPagePredictRewardDataDetailOpened',
  },
}

class TemplateService {
  constructor() {}

  getTemplateInfo(aid: string | number, previewId?: string, isUseMock?: boolean, mockUserRank?: string) {
    return new Promise((resolve) => {
      let cgi = focusCore.hasAccount ? cgis.getTemplateInfo : cgis.getTemplateInfoNoAuth

      if (isUseMock) {
        cgi = cgis.getPreviewTemplateData
      }
      let params = {
        activityId: aid,
        isBonus: isUseMock ? '1' : '0',
        rank: mockUserRank || '',
        previewId: previewId || '',
      }

      focusCore
        .request(cgi, params)
        .then((res: TPageConfig) => {
          console.log('🚀 ~ TemplateService ~ getTemplateInfo ~ res:', res)
          const { pageInfo = {}, templateType = 'PRODUCT_BUY', participateMethod } = res
          const {
            topInfo = {},
            bannerCardList = [],
            shareInfo,
            pageAccessDesc,
            pageTitle,
            pointsRedeem,
            selectionReason,
            partnerWealth,
          } = pageInfo

          const pageBgs: any = {
            PRODUCT_BUY: '#deefff',
            SINGLE_BUY: '#ffe3c0',
            AIP: '#ffe8e3',
          }

          resolve({
            actNumber: topInfo.activityCode,
            topBanner: topInfo.headImageUrl,
            bannersInfo: bannerCardList,
            ruleText: topInfo.rewardWay,
            pageBg: pageBgs[templateType],
            shareInfo,
            showBtnAdding: participateMethod?.toString() === '1',
            autoJoinAid: participateMethod?.toString() === '2',
            pageTimeError:
              pageAccessDesc === '1'
                ? '活动还没开始，请耐心等待哦'
                : pageAccessDesc === '2'
                ? '活动已结束，下次再来参与吧~'
                : '',
            showPointsRedeem: pointsRedeem === '1',
            showSelectionReason: selectionReason === '1',
            showPartnerWealth: partnerWealth === '1',
            pageTitle: pageTitle || '微众银行',
          })
        })
        .catch((err: any) => {
          console.log('🚀 ~ file: service.ts:110 ~ TemplateService ~ getTemplateInfo ~ err:', err)
          resolve({})
        })
    })
  }

  getTemplateRewardInfo(aid: string | number) {
    let cgi = cgis.getActProgressData

    return new Promise((resolve) => {
      focusCore
        .request(cgi, {
          activityId: aid,
        })
        .then(
          (res: {
            predictReward?: {
              templateSubType: string //模板子类型：01-规模型-单只产品计算，02-规模型-跨产品合并计算，03-达标型-多次产品分别计算，04-达标型-任意一只达标奖励一次，05-达标型-跨产品合并只奖励一次，06-基金定投

              predictRewardData?: {
                bonusInfo: {
                  // 是否加码：1-是，0-否
                  isBonus?: string
                  bonusType?: string // 加码类型，加码奖励类型：PERCENTAGE-比例，ABSOLUTE-绝对值
                  // 加码比例（对应加码类型为PERCENTAGE）
                  bonusPercentage?: number
                  // 加码绝对值（对应加码类型为ABSOLUTE）
                  bonusAmount?: number
                  // 新财富+用户等级,1:白银,2:黄金,3:铂金,4:钻石
                  wealthRank?: string
                }
                // 购买金额计算方式，0-申购，1-净申购
                buyAmountCalcWay?: string
                // 购买总金额
                buyAmountSum?: number
                // 预估总积分
                previewPointsSum?: number
                // 预估基础总积分
                previewBasePointsSum?: number
                // 预估加码总积分
                previewBonusPointsSum?: number
                // 是否有赎回在途金额；1-是，0否
                isPendingRedemptionAmount?: string
                // 赎回时间，空则不展示
                redeemTime?: string
              }
            }
          }) => {
            console.log('🚀 ~ TemplateService ~ getTemplateRewardInfo ~ res:', res)
            const { predictReward } = res
            const { templateSubType, predictRewardData = {} } = predictReward || {}
            const {
              bonusInfo = {},
              redeemTime,
              previewPointsSum,
              previewBasePointsSum,
              previewBonusPointsSum,
              isPendingRedemptionAmount,
              buyAmountCalcWay,
            } = predictRewardData || {}
            const { isBonus, bonusType, bonusPercentage, bonusAmount, wealthRank } = bonusInfo

            let btnTipText = ''
            // 净申购
            if (buyAmountCalcWay === '1') {
              btnTipText = redeemTime ? `${dayjs(redeemTime).format('YYYY-MM-DD')}前赎回影响部分积分奖励发放` : ''
            }
            // 申购
            if (buyAmountCalcWay === '0') {
              btnTipText = redeemTime ? `${dayjs(redeemTime).format('YYYY-MM-DD')}前赎回无积分奖励` : ''
            }

            resolve({
              actData: {
                templateSubType,
                previewPointsSum,
                previewBasePointsSum: isBonus === '1' ? previewBasePointsSum : '',
                bonusText:
                  isBonus === '1'
                    ? `已多奖${bonusType === 'PERCENTAGE' ? bonusPercentage + '%' : bonusAmount}积分`
                    : '',
                btnTipText,
                buyAmountCalcWay,
                showRedemptionTip: isPendingRedemptionAmount === '1',
              },
            })
          },
        )
        .catch((err: any) => {
          console.log('🚀 ~ file: service.ts:110 ~ TemplateService ~ getTemplateRewardInfo ~ err:', err)
          resolve({})
        })
    })
  }

  getActProgressList(aid: string | number) {
    let cgi = cgis.getActProgressList

    return new Promise((resolve) => {
      focusCore
        .request(cgi, {
          activityId: aid,
        })
        .then(
          (res: {
            predictRewardDataDetail: {
              productCode: string
              productName: string
              buyAmount: number
              previewPoints: number
              previewBasePoints: number
              previewBonusPoints: number
            }[]
          }) => {
            console.log('🚀 ~ TemplateService ~ getActProgressList ~ res:', res)
            resolve(res)
          },
        )
        .catch((err: any) => {
          console.log('🚀 ~ file: service.ts:110 ~ TemplateService ~ getActProgressList ~ err:', err)
          resolve({})
        })
    })
  }

  getTemplateProductInfo(aid: string | number, previewId?: string, isUseMock?: boolean, mockUserRank?: string) {
    return new Promise((resolve, reject) => {
      let cgi = focusCore.hasAccount ? cgis.getTemplateProductInfo : cgis.getTemplateProductInfoNoAuth
      if (isUseMock) {
        cgi = cgis.getPreviewProductInfoData
      }
      focusCore
        .request(cgi, {
          activityId: aid,
          rank: mockUserRank || '',
          isBonus: isUseMock ? '1' : '0',
          previewId: previewId || '',
        })
        .then((res: TTemplateData & { ret_code: string }) => {
          console.log('🚀 ~ TemplateService ~ getTemplateProductInfo ~ res:', res)
          if (!/0000$/.test(res.ret_code)) {
            reject({
              error: true,
            })
            return
          }
          const { rewardRule = {} } = res
          const {
            rewardList = [],
            templateSubType = '',
            productTypeDesc = '1',
            ruleType = '',
            bonusType,
            bonusPercentage,
            buyAmountCalcWay,
            isBonus,
            bonusGroupType,
            wealthRank,
            earningsRateDate,
            multiProductCalcTogether,
            allowRedeem,
            isCanSeeProduct,
          } = rewardRule

          // console.log('🚀 ~ TemplateService ~ getTemplateProductInfo ~ rewardList:', rewardList)

          let bonusText = ''
          if (isBonus === '1') {
            const rankText: any = {
              '1': '白银',
              '2': '黄金',
              '3': '铂金',
              '4': '钻石',
            }
            const bonusGroupTypeText: any = {
              CUST_GROUP: '受邀客户',
              MEMBER_LEVEL: '会员客户',
              ENTERPRISE_AUTH: '已企业认证',
              NEW_CUST: '受邀新客户',
            }
            let bonusUser =
              bonusGroupType === 'MEMBER_LEVEL'
                ? `${rankText[wealthRank || '1']}会员`
                : bonusGroupTypeText[bonusGroupType || '']

            bonusText = `${bonusUser}多奖${bonusType === 'PERCENTAGE' ? bonusPercentage + '%' : bonusPercentage}积分`
          }

          let prodCardList = rewardList.map((item) => {
            const { requiredAmount, rewardPoints, aipWay, requiredTimes } = item
            // console.log('🚀 ~ TemplateService ~ getTemplateProductInfo ~ item:', item)

            const btnTexts: any = {
              '01': '去买入',
              '02': '已售罄',
              '03': '已截售',
            }

            let ruleTextTitle = ''
            let ruleTextDesc = ''
            let ruleDataType = ''
            ruleTextDesc =
              buyAmountCalcWay === '1' ? '奖励按活动期间买入减去赎回金额计算' : '奖励按照活动期间买入金额计算'
            if (templateSubType === '01') {
              ruleDataType = '1'
              ruleTextTitle = `单只产品买入每${requiredAmount}元`
            }

            if (templateSubType === '02') {
              ruleDataType = '2'
              ruleTextTitle = `以下产品累计买入每${requiredAmount}元`
            }

            if (templateSubType === '03') {
              ruleDataType = '3'
              ruleTextTitle = `任意一只产品买入满${requiredAmount}元`
            }

            if (templateSubType === '04') {
              ruleDataType = '4'
              ruleTextTitle = `任意一只产品买入满${requiredAmount}元`
            }

            if (templateSubType === '05') {
              ruleDataType = '5'
              ruleTextTitle = `以下产品累计买入满${requiredAmount}元`
            }

            if (templateSubType === '06') {
              const aipWays: any = {
                '1': '每天',
                '2': '每周',
                '3': '每双周',
                '4': '每月',
              }
              ruleDataType = '6'
              ruleTextTitle = `每只${aipWays[aipWay || 'EVERY_WEEK']}投`
            }

            return {
              ...item,
              ruleType,
              templateSubType,
              ruleData: {
                ruleDataType,
                ruleTextTitle,
                ruleTextDesc,
                rewardPoints,
                rewardBonusText:
                  isBonus === '1' ? `多奖${bonusType === 'PERCENTAGE' ? bonusPercentage + '%' : bonusPercentage}` : '',
                requiredAmount,
                requiredTimes,
              },
              productList:
                (item.productList &&
                  item.productList.map((i) => {
                    let formateRateValue = []
                    if (i.rateValue && i.rateValue?.indexOf('-') > -1 && i.rateValue !== '--') {
                      formateRateValue = i.rateValue.split('-').map((_i) => {
                        return _i.replace('%', '')
                      })
                    } else {
                      formateRateValue = [i.rateValue ? i.rateValue?.replace('%', '') : '--']
                    }

                    return {
                      ...i,
                      rateValue:
                        i.rateValue && i.rateValue.indexOf('%') > -1 ? i.rateValue.replace('%', '') : i.rateValue,
                      formateRateValue,
                      btnText: i.saleStatus ? btnTexts[i.saleStatus] : '去买入',
                      isSaleOut: i.saleStatus !== '01',
                    }
                  })) ||
                [],
            }
          })

          resolve({
            prodCardList: prodCardList,
            templateSubType,
            productTypeDesc,
            ruleType,
            bonusText,
            earningsRateDate,
            earningsRateDateFormat: dayjs(earningsRateDate).format('YYYY-MM-DD'),
            wealthRank,
            bonusGroupType,
            isCanSeeProduct,
          })
        })
        .catch((err: any) => {
          reject
        })
    })
  }
}
export default new TemplateService()
