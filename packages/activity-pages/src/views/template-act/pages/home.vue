<template>
  <skeleton :show="showSkelenton"></skeleton>
  <div class="wrap" :class="`${uiStyle.type}`" :style="{ opacity: dataIsReady ? 1 : 0 }">
    <div class="top-area">
      <img class="top-banner" :src="topBanner" />
      <number-space-around-text
        v-if="productData.bonusText"
        :text="productData.bonusText"
        class="bonus-text"
        :textSize="20"
      >
      </number-space-around-text>

      <div class="btn-rule" @click="clickRule">规则</div>
    </div>

    <!-- 规则类型：SINGLE_BUY-单次购买, CUMULATIVE_BUY-累计购买, AIP-基金定投 -->

    <!-- 产品购买，单只产品每满x元 -->
    <div class="act-data" v-show="actProgressData.previewPointsSum">
      <div class="row">
        <div class="left title">累计买入<span>(元)</span></div>
        <div class="right title">
          预估奖励积分
          <BtnTipCover
            v-if="actProgressData.btnTipText"
            :boxWidth="actProgressData.buyAmountCalcWay === '0' ? 364 : 492"
            :contentLeftTranslate="actProgressData.buyAmountCalcWay === '0' ? 182 : 326"
            :tipText="`${actProgressData.btnTipText}`"
            :iconColor="uiStyle.actDataTipColor"
          ></BtnTipCover>
        </div>
      </div>
      <div class="row line2">
        <div class="left money">{{ actProgressData.previewPointsSum }}</div>
        <div class="right point" :class="{ 'is-zero': actProgressData.previewPointsSum === '0' }">
          <p>
            {{ actProgressData.previewPointsSum }}
          </p>
          <p class="base-point" v-show="actProgressData.previewBasePointsSum">
            {{ actProgressData.previewBasePointsSum }}
          </p>
        </div>
      </div>
      <div class="row line3" v-show="actProgressData.bonusText">
        <div class="left"></div>
        <div class="right">
          <div class="bonus-text-box" v-if="actProgressData.bonusText">
            <number-space-around-text class="bonus-point" :text="actProgressData.bonusText"></number-space-around-text>
          </div>
        </div>
      </div>
      <div class="row line4" v-show="actProgressData.showRedemptionTip">
        <div class="left">
          <p>存在赎回在途金额，确认后更新预估奖励积分</p>
        </div>
      </div>
    </div>

    <!-- 导航锚点 -->
    <anchor-nav
      v-if="productData.prodCardList.length > 1"
      :uiType="uiStyle.type"
      :productList="productData.prodCardList"
      :productCardTopList="productCardTopList"
    ></anchor-nav>

    <!-- 产品卡片 -->
    <prodcode-cards
      :codeDatas="item.productList"
      :ruleData="item.ruleData"
      :rateStatisicsDate="productData.rateStatisicsDate"
      v-for="(item, index) in productData.prodCardList"
      :key="index"
      :id="`product_${index}`"
      @onTopIsReady="(top) => updateProductCardTop(index, top)"
      :clickAddingAid="canShowBtnAdding ? activityId : ''"
    ></prodcode-cards>

    <p class="earnings-rate-date" v-if="productData.prodCardList.length">
      收益率统计至：{{ productData.earningsRateDate }}
    </p>

    <p class="banner-title" v-if="bannerCardList.length">更多活动</p>
    <div class="banner-card" v-for="(item, index) in bannerCardList" :key="index" @click="clickBannerCard(item)">
      <img :src="item.displayImageUrl" alt="" />
    </div>

    <!-- 积分兑好礼 -->
    <div class="static-card point-card" v-show="showPointsRedeem">
      <p class="title">积分兑好礼</p>
      <div class="list">
        <div class="item">
          <img src="../img/goods-wechat.png" alt="" />
          <p>微信立减金</p>
          <number-space-around-text :text="'500积分'" class="sub"></number-space-around-text>
        </div>
        <div class="item">
          <img src="../img/goods-phone-bill.png" alt="" />
          <number-space-around-text :text="'100元话费'"></number-space-around-text>

          <number-space-around-text :text="'10000积分'" class="sub"></number-space-around-text>
        </div>
        <div class="item">
          <img src="../img/goods-jd.png" alt="" />
          <number-space-around-text :text="'100元京东卡'"></number-space-around-text>

          <number-space-around-text :text="'10000积分'" class="sub"></number-space-around-text>
        </div>
      </div>
      <div class="btn-link" @click="clickPointLink" v-show="!isInWxMini">更多礼品兑换</div>
    </div>

    <!-- 选择微众银行 -->
    <div class="static-card about-us" v-show="showSelectionReason">
      <p class="title">为什么选择微众银行</p>
      <div class="list">
        <div class="item">
          <img class="icon" src="../img/aboutus-leading.png" alt="" />
          <p>全球领先</p>
          <p class="sub">《亚洲银行家》评选</p>
          <p class="sub">超4亿客户合作机构</p>
          <BtnTipCover
            class="btn-tip"
            :iconColor="uiStyle.aboutUsTipColor"
            :tipText="'*来源自THE ASIAN BANKER（亚洲银行家）2022-2023年度国际卓越零售金融服务奖项'"
            :boxWidth="520"
            :contentLeftTranslate="220"
          ></BtnTipCover>
        </div>
        <div class="item">
          <img class="icon" src="../img/aboutus-top100.png" alt="" />
          <p>百强品牌</p>
          <p class="sub">连续六年入选</p>
          <p class="sub">中国银行业100强</p>
        </div>
        <div class="item">
          <img class="icon" src="../img/aboutus-customers.png" alt="" />
          <p>亿万客户</p>
          <p class="sub">超4亿客户合作机构</p>
          <p class="sub">资产管理规模超万亿元</p>
        </div>
      </div>
      <div class="btn-link" @click="clickAbouteUs">了解微众银行</div>
    </div>

    <!-- 理财小知识 -->
    <div class="card-xiaozhishi">
      <p v-show="!hasFundProd">
        <span>【理财小知识】</span
        >银行理财子公司的产品基本采用净值化管理，投资端更透明，客户也能清晰地了解产品投资的真实涨跌。
      </p>

      <p v-show="hasFundProd">
        <span>【理财小知识】</span
        >鸡蛋不能放在一个篮子里，购买基金需要建立多元投资组合，利用不同资产间的风险差异，起到分散和降低风险的作用。
      </p>
    </div>

    <div class="static-card companys" v-show="showPartnerWealth">
      <p class="title">合作银行理财子公司达25家</p>
      <div class="bank-list">
        <div class="item" v-for="(item, index) in bankList" :key="index">
          <img :src="item.bankIcon" alt="" />
          <p>{{ item.bankName }}</p>
        </div>
      </div>
      <div class="btn-link" @click="clickCompany" v-show="!isInWxMini">了解详情</div>
    </div>

    <!-- 风险提示 -->
    <div class="warning-text">
      <p class="title">风险提示：</p>
      <p v-show="hasWealthProd">
        &nbsp;&nbsp;&nbsp;&nbsp;理财产品由管理人发行与管理，微众银行不承担产品的投资、兑付和风险管理责任，过往业绩不代表其未来表现、不构成对收益的承诺，理财非存款、产品有风险、投资须谨慎。
      </p>
      <p v-show="hasFundProd">
        &nbsp;&nbsp;&nbsp;&nbsp;基金有风险，投资需谨慎。基金产品由基金管理人发行与管理，微众银行股份有限公司作为代销机构不承担产品的投资、兑付责任。基金管理人承诺以诚实信用、勤勉尽责的原则管理和运用基金资产，但不保证一定盈利，也不保证最低收益，基金的过往业绩及其净值高低并不预示其未来业绩表现。您在做出投资决策之前，请仔细阅读基金合同、基金招募说明书和基金产品资料概要等产品法律文件和风险揭示书，充分认识基金产品的风险收益特征和产品特性，并根据自身的投资目的、投资期限、投资经验、资产状况等因素，充分考虑自身的风险承受能力，在了解产品情况及销售适当性意见的基础上，理性判断并谨慎做出投资决策。
      </p>
    </div>

    <!-- 活动编号 -->
    <div class="active-number">
      <p>活动编号：{{ actNumber }}</p>
    </div>

    <img
      class="logo"
      v-show="pageUi.logoColor"
      src="https://dbd.webankwealthcdn.net/wm-resm/hjupload/common/logo/logo-color.png"
      alt=""
    />
    <img
      class="logo"
      v-show="pageUi.logoWhite"
      src="https://dbd.webankwealthcdn.net/wm-resm/hjupload/common/logo/logo-white.png"
      alt=""
    />

    <div class="btn-adding" :class="isAddingAid ? 'is-adding' : ''" v-show="canShowBtnAdding" @click="clickAdding">
      {{ isAddingAid ? '已报名' : '立即报名' }}
    </div>

    <dialog-mask :show="showRule" :canScroll="true">
      <template #dialog_contain>
        <div class="rule-dialog" :class="uiStyle.type">
          <div class="btn-close" @click="showRule = false"></div>
          <div class="dialog-header">
            <p class="title">活动规则</p>
          </div>
          <div class="dialog-body">
            <div class="dialog-body-content" v-html="ruleHtml"></div>
          </div>
        </div>
      </template>
    </dialog-mask>
  </div>
  <focus-render :hideWxGuest="true" @onLoginSucc="afterLogin" @onFocusConfigSucc="setFocus"></focus-render>
</template>

<script setup lang="ts">
import { focusCore, focusStore, focusServices, Mask, focusUtils, DialogMask } from '@focus/render'
import { computed, reactive, ref, watchEffect } from 'vue'
import prodcodeCards from '../components/ProdcodeCard.vue'
import AnchorNav from '../components/AnchorNav.vue'
import NumberSpaceAroundText from '../components/NumberSpaceAroundText.vue'
import templateService from '../service/service'
import Skeleton from '../components/Skeleton.vue'
import BtnTipCover from '../components/BtnTipCover.vue'
import service from '../service/service'
const { jumpService, activityService, mgmService, wxServcie } = focusServices
const emit = defineEmits(['initSuccess'])
const { UrlParser, dayjs } = focusUtils
const locationHrefPared = new UrlParser(window.location.href)
const { activityId = 0, activityUIType = '1', navColor = '', mockUserRank, previewId } = locationHrefPared.query
const { modalStore } = focusStore.stores
const isAddingAid = ref(false)
const productCardTopList = ref<number[]>([])
const showRule = ref(false)
const showSkelenton = ref(true)
const canShowBtnAdding = ref(false)
const dataIsReady = ref(false)
const showPointsRedeem = ref(false)
const showSelectionReason = ref(false)
const showPartnerWealth = ref(false)
const isInWxMini = ref(false)
const props = defineProps({
  useMock: {
    type: Boolean,
    default: false,
  },
})

// displayImageUrl: "https://dbd.test.webankwealthcdn.net/wm-resm/hjupload/op-fmadmin/1756795979496_702.png"
// jumpLink: "rewardPoint/RewardPointHomeScene"
// jumpLinkSign: ""
// jumpType: "appModule"
const bannerCardList = ref([])
const pageUi = reactive({
  logoColor: true,
  logoWhite: false,
})
// 预估积分的小弹窗
const showRewardTip = ref(false)
// 活动规则
const ruleText = ref('')
// 活动编号
const actNumber = ref('')
const hasWealthProd = ref(false)
const hasFundProd = ref(false)
const pageTitle = ref('')
const uiStyle = reactive({
  aboutUsTipColor: '#FFB852',
  actDataTipColor: '#808BAB',
  type: 'type1',
})
const prodcodeCardsRef = ref(null)
const actProgressData = ref({
  templateSubType: '',
  previewPointsSum: '0',
  previewBasePointsSum: '0',
  bonusText: '',
  btnTipText: '',
  showRedemptionTip: false,
  buyAmountCalcWay: '',
})
const shareData = ref<any>({})
// 活动规则
const ruleHtml = computed(() => {
  console.log('🚀 ~ ruleText.value:', ruleText.value)

  const strs = ruleText.value.split(/<b>/)
  console.log('🚀 ~ strs:', strs)
  const result: string[] = []

  strs.forEach((i) => {
    let str = i
    if (i) {
      if (i.indexOf('</b>')) {
        const arr = i.split('</b>')
        const firstStr = arr[0]
        const lastStr = arr[1]
        result.push(`<p class="title">${firstStr}</p>`)
        str = lastStr
      }
      if (str.indexOf('\\n') > -1) {
        const arr = str.split('\\n')
        arr.forEach((i) => {
          i && result.push(`<p>${i}</p>`)
        })
      } else {
        result.push(`<p class="title">${str}</p>`)
      }
    }
  })

  console.log('🚀 ~ result:', result)
  if (focusCore.env.device === 'ios') {
    result.push('<p style="margin-top: 20px;">注: 本次活动与苹果公司(Apple lnc.)无关</p>')
  }

  return result.join('')
})

const userStatus = reactive({
  hasAccount: false,
  isBoosted: false,
})

const topBanner = ref('')

const bankList = [
  { bankName: '兴银理财', bankIcon: require('../img/icon-banks/兴银理财.png') },
  { bankName: '光大理财', bankIcon: require('../img/icon-banks/光大理财.png') },
  { bankName: '交银理财', bankIcon: require('../img/icon-banks/交银理财.png') },
  { bankName: '平安理财', bankIcon: require('../img/icon-banks/平安理财.png') },
  { bankName: '信银理财', bankIcon: require('../img/icon-banks/信银理财.png') },
  { bankName: '中银理财', bankIcon: require('../img/icon-banks/中银理财.png') },
  { bankName: '青银理财', bankIcon: require('../img/icon-banks/青银理财.png') },
  { bankName: '华夏理财', bankIcon: require('../img/icon-banks/华夏理财.png') },
  { bankName: '南银理财', bankIcon: require('../img/icon-banks/南银理财.png') },
  { bankName: '苏银理财', bankIcon: require('../img/icon-banks/苏银理财.png') },
  { bankName: '杭银理财', bankIcon: require('../img/icon-banks/杭银理财.png') },
  { bankName: '渝农商理财', bankIcon: require('../img/icon-banks/渝农商理财.png') },
  { bankName: '招银理财', bankIcon: require('../img/icon-banks/招银理财.png') },
  { bankName: '宁银理财', bankIcon: require('../img/icon-banks/宁银理财.png') },
  { bankName: '徽银理财', bankIcon: require('../img/icon-banks/徽银理财.png') },
  { bankName: '中邮理财', bankIcon: require('../img/icon-banks/中邮理财.png') },
  { bankName: '农银理财', bankIcon: require('../img/icon-banks/农银理财.png') },
  { bankName: '民生理财', bankIcon: require('../img/icon-banks/民生理财.png') },
  { bankName: '浦银理财', bankIcon: require('../img/icon-banks/浦银理财.png') },
  { bankName: '广银理财', bankIcon: require('../img/icon-banks/广银理财.png') },
  { bankName: '渤银理财', bankIcon: require('../img/icon-banks/渤银理财.png') },
  { bankName: '上银理财', bankIcon: require('../img/icon-banks/上银理财.png') },
  { bankName: '北银理财', bankIcon: require('../img/icon-banks/北银理财.png') },
  { bankName: '恒丰理财', bankIcon: require('../img/icon-banks/恒丰理财.png') },
  { bankName: '工银理财', bankIcon: require('../img/icon-banks/工银理财.png') },
]

const rewrdData = reactive({
  typeText: '',
  money: '',
  point: 0,
  isSigned: false,
})

const productData = reactive({
  prodCardList: [],
  rateStatisicsDate: '',
  templateSubType: '',
  productTypeDesc: '',
  ruleType: '',
  bonusText: '',
  earningsRateDate: '',
})

watchEffect(() => {
  if (!focusCore.env.isInApp && dataIsReady.value && pageTitle.value) {
    console.log('🚀 ~ setFocus ~ pageTitle.value:', pageTitle.value)
    focusCore.setPageTitle(pageTitle.value)
  }
})

checkUiType()

function checkUiType() {
  console.log('🚀 ~ checkUiType ~ navColor:', navColor)
  const types: any = {
    deefff: 'type1',
    ffe3c0: 'type2',
    ffe8e3: 'type3',
  }

  uiStyle.type = types[navColor.toLowerCase()] || 'type1'

  // 为什么选择微众银行-全球领先 小i颜色
  uiStyle.aboutUsTipColor = uiStyle.type === 'type1' ? '#FFB852' : '#B0818D'
  // 活动进度小i颜色
  uiStyle.actDataTipColor = uiStyle.type === 'type1' ? '#FFB852' : '#C6AFB4'

  document.querySelector('body')?.setAttribute('style', `background: #${navColor || '#deefff'};`)
}

function clickRule() {
  console.log('1111')
  showRule.value = true
}

function doSomeThingAfterErr() {
  if (!focusCore.env.isInApp) {
    focusCore.setPageTitle(pageTitle.value)
  }
}

function init() {
  if (focusCore.env.isInApp) {
    window.hjCoreIab.setNavBar({
      title: ' ',
      useAutoFixNavBar: true,
    })
  }
  if (!activityId) {
    modalStore.errorMaskContrl('noactivityId')
    return
  }

  modalStore.loadingStart('init')
  activityService.checkWrInfoStatus(activityId).then((res) => {
    isAddingAid.value = !!res
  })
  getActProgressListData()

  // 并行处理接口
  Promise.all([
    activityService.checkAidsIsReady(activityId),
    templateService.getTemplateInfo(activityId, previewId, props.useMock, mockUserRank),
    templateService.getTemplateRewardInfo(activityId),
    templateService.getTemplateProductInfo(activityId, previewId, props.useMock, mockUserRank),
  ])
    .then((res) => {
      console.log('🚀 ~ init ~ res:', res)
      const [checkactivityIdsIsReady, templateInfo = {}, rewardInfo, productInfo] = res
      console.log('🚀 ~ init ~ rewardInfo:', rewardInfo)
      console.log('🚀 ~ init ~ checkactivityIdsIsReady:', checkactivityIdsIsReady)
      console.log('🚀 ~ init ~ templateInfo:', templateInfo)
      console.log('🚀 ~ init ~ productInfo:', productInfo)

      pageTitle.value = templateInfo.pageTitle || '微众银行'

      if (!checkactivityIdsIsReady.isSupportNoAuth && !focusCore.hasAccount) {
        modalStore.errorMaskContrl(`not_support_noauth_${activityId}`, ['非常抱歉，本活动仅限受邀客户参与'])
        doSomeThingAfterErr()
        return
      }
      if (checkactivityIdsIsReady.isUsedAndNotInGray) {
        modalStore.errorMaskContrl(`not_ingray_aid_${activityId}`, ['非常抱歉，本活动仅限受邀客户参与'])
        doSomeThingAfterErr()

        return
      }

      if (productInfo.isCanSeeProduct === '0') {
        modalStore.errorMaskContrl(`no_product_${activityId}`, ['非常抱歉，本活动仅限受邀客户参与'])
        doSomeThingAfterErr()
        return
      }

      if (templateInfo.pageTimeError) {
        modalStore.errorMaskContrl('pageTimeError', [templateInfo.pageTimeError])
        doSomeThingAfterErr()
        return
      }

      dataIsReady.value = true

      actProgressData.value = rewardInfo.actData || {}

      shareData.value = templateInfo.shareInfo || {}
      bannerCardList.value = templateInfo.bannersInfo || []
      ruleText.value = templateInfo.ruleText || ''
      topBanner.value = templateInfo.topBanner || ''
      actNumber.value = templateInfo.actNumber || ''
      canShowBtnAdding.value = templateInfo.showBtnAdding
      showPointsRedeem.value = templateInfo.showPointsRedeem
      console.log('🚀 ~ init ~ showPointsRedeem.value:', showPointsRedeem.value)
      showSelectionReason.value = templateInfo.showSelectionReason
      console.log('🚀 ~ init ~ showSelectionReason.value:', showSelectionReason.value)
      showPartnerWealth.value = templateInfo.showPartnerWealth
      console.log('🚀 ~ init ~  showPartnerWealth.value:', showPartnerWealth.value)

      if (templateInfo.autoJoinAid) {
        activityService.setWrInfo(activityId).then((res) => {
          console.log('🚀 ~ init ~ setWrInfo res:', res)
        })
      }

      if (rewardInfo.status === 1) {
        rewrdData.typeText = rewardInfo.typeText
        rewrdData.money = rewardInfo.money
        rewrdData.point = rewardInfo.point
        rewrdData.isSigned = rewardInfo.isSigned
        userStatus.isBoosted = rewardInfo.isBoosted
      }
      productData.prodCardList = productInfo.prodCardList || []
      productData.rateStatisicsDate = productInfo.rateStatisicsDate
      productData.templateSubType = productInfo.templateSubType
      productData.productTypeDesc = productInfo.productTypeDesc
      productData.ruleType = productInfo.ruleType
      productData.bonusText = productInfo.bonusText
      console.log('🚀 ~ init ~  productData.bonusText:', productData.bonusText)
      productData.earningsRateDate = productInfo.earningsRateDate && productInfo.earningsRateDateFormat

      hasWealthProd.value = productInfo.productTypeDesc !== '2'
      hasFundProd.value = productInfo.productTypeDesc !== '1'

      console.log('🚀 ~ init ~ productData.codeDatas:', productData.codeDatas)

      initShare()
      emit('initSuccess', {
        wealthRank: productInfo.wealthRank,
        bonusGroupType: productInfo.bonusGroupType,
      })
    })
    .catch((err) => {
      console.log('🚀 ~ init ~ promise all err:', err)
      modalStore.errorMaskContrl('initError')
    })
    .finally(() => {
      console.log('finally.....')
      modalStore.loadingEnd('init')
      showSkelenton.value = false
    })
}

function initShare() {
  console.log('🚀 ~ initShare ~ shareData.value:', shareData.value)
  const { appSharePanel, shareButton, shareContent, shareImageUrl, shareUrl, shareLink, shareTitle, sharePosterUrl } =
    shareData.value
  const shareTypes: any = {
    '1': '0',
    '2': '1',
    '3': '2',
    '4': '3',
  }
  if (focusCore.env.isInApp) {
    mgmService.setRtConnerShare('template-act', {
      shareTitle,
      shareUrl: shareLink,
      shareImg: shareImageUrl,
      shareDesc: shareContent,
      appShareType: shareTypes[appSharePanel] || '0',
      shareImageBg: appSharePanel === '4' ? '' : sharePosterUrl,
      shareImageHeight: '800',
    })
  } else {
    wxServcie.init().then((canDoWx) => {
      if (canDoWx) {
        mgmService.setRtConnerShare('template-act', {
          shareTitle,
          shareUrl: shareLink,
          shareImg: shareImageUrl,
          shareDesc: shareContent,
        })
      }
    })
  }
}

function clickBannerCard(data: any) {
  console.log('🚀 ~ clickBannerCard ~ data:', data)
  if (data.jumpType === 'appModule' && isInWxMini.value) {
    modalStore.toastShow('请前往App查看详情')
    return
  }

  jumpService.jump({
    path: data.jumpLink,
    method: data.jumpType,
  })
}
function afterLogin(logindata: any) {
  console.log('🚀 ~ afterLogin ~ afterLogin:', logindata)
  console.log('afterLogin')
  userStatus.hasAccount = logindata.hasAccount
  init()
}
function setFocus(focusCtrl: any) {
  console.log('🚀 ~ setFocus ~ setFocus:', focusCtrl)
  console.log('setFocus')
  // if (window.hjCoreIab && window.hjCoreIab.setNavBar) {
  //   window.hjCoreIab.setNavBar({
  //     useAutoFixNavBar: true,
  //     rightButtonConfig: {
  //       type: 'share',
  //       shareConfig: {},
  //     },
  //   })
  // }
  console.log('🚀 ~ setFocus ~ focusCore.env.isInApp:', focusCore.env.isInApp)
  focusCore.env.checkIsInWxMini().then((res: boolean) => {
    isInWxMini.value = res
  })
}

function getActProgressListData() {
  service.getActProgressList(activityId).then((res) => {
    console.log('🚀 ~ getActProgressListData ~ res:', res)
  })
}

function clickBannerSinge() {
  console.log('clickBannerSinge')
  jumpService.jump(banner1.clickEvent)
}

function clickPointLink() {
  // jumpService.jumpNewWebview('https://m.test.webank.com/s/hj/focus2/client/index.html?fid=168')

  jumpService.jump({
    path: '/rewardPoint/RewardPointHomeScene',
  })
}

function clickAbouteUs() {
  jumpService.jumpNewWebview('https://www.webank.com/about')
  // jumpService.jump({
  //   path: 'https://www.webank.com/about',
  // })
}

function updateProductCardTop(index: number, top: number) {
  console.log('🚀 ~ updateProductCardTop ~ top:', top)
  console.log('🚀 ~ updateProductCardTop ~ index:', index)
  productCardTopList.value[index] = top
}

function clickCompany() {
  jumpService.jump({
    path: '/finance/FinanceBankScene',
  })
}

function clickAdding() {
  activityService.setWrInfo(activityId).then(() => {
    isAddingAid.value = true
    modalStore.toastShow('报名成功')
  })
}
</script>

<style lang="scss" scoped>
.type1 {
  --text-color-1: #405080;
  --text-color-2: #b4bacc;
  --text-color-3: #808bab;
  // 产品卡片颜色
  --prodcard-color-1: #405080;
  --prodcard-color-2: #b4bacc;
  --prodcard-color-3: #b0818d;
  --prodcard-color-4: #f24c3d;
  // 奖励卡颜色
  --reward-color-1: #9b431e;
  --reward-color-2: #c6a391;
  --reward-color-3: #f6d0c1;
  --reward-color-4: #f24c3d;
}

.type2,
.type3 {
  --text-color-1: #61041b;
  --text-color-2: #c6afb4;
  --text-color-3: #b0818d;
  // 产品卡片颜色
  --prodcard-color-1: #61041b;
  --prodcard-color-2: #c6afb4;
  --prodcard-color-3: #b0818d;
  --prodcard-color-4: #f24c3d;
  // 奖励卡颜色
  --reward-color-1: #9b431e;
  --reward-color-2: #c6a391;
  --reward-color-3: #f6d0c1;
  --reward-color-4: #f24c3d;
}
.banner-title {
  color: var(--text-color-1);
  font-size: 28px;
  font-style: normal;
  font-weight: 400;
  margin-top: 20px;
  margin-bottom: 20px;
  line-height: 30px;
  height: 30px;
}
.banner-card {
  width: 706px;
  height: 140px;
  margin: 0 auto;
  margin-top: 24px;
  background-color: #eee;
  border-radius: 16px;
  overflow: hidden;
  img {
    width: 100%;
    height: auto;
  }
}

.wrap {
  width: 100%;
  margin: 0 auto;
  height: auto;
}

.top-area {
  width: 100%;
  height: auto;
  min-height: 193px;
  position: relative;
  .bonus-text {
    font-size: 18px;
    color: #a55121;
    letter-spacing: 0;
    background: linear-gradient(to right, #ffd2e083, rgba(255, 248, 228, 0));
    position: absolute;
    top: 8px;
    right: 30px;
    width: 218px;
    box-sizing: border-box;
    height: 30px;
    line-height: 30px;
    padding-left: 8px;
    border-radius: 4px;
    text-align: left;
  }
  .btn-rule {
    width: 33px;
    height: 63px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 0;
    top: 46px;
    background-color: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px 0 0 10px;
    font-size: 20px;
    color: var(--text-color-1);
    letter-spacing: 0;
    line-height: 22px;
    cursor: pointer;
  }
}

.rule-dialog {
  width: 702px;
  box-sizing: border-box;
  max-height: 75vh;
  background-color: #fdfdfd;
  border-radius: 16px;
  position: relative;
  font-size: 24px;
  color: var(--text-color-1);
  letter-spacing: 0;
  :deep(span.bold) {
    font-weight: bold;
  }
  .btn-close {
    width: 60px;
    height: 60px;
    position: absolute;
    right: 0;
    top: -100px;
    background: url('../img/icon-btn-close.png') no-repeat;
    background-size: 100% 100%;
  }
  .dialog-header {
    font-size: 32px;
    color: var(--text-color-1);
    letter-spacing: 0;
    line-height: 48px;
    font-weight: bold;
    text-align: center;
    padding-top: 32px;
  }
  .dialog-body {
    height: calc(100% - 100px);
    overflow-y: auto;
    box-sizing: border-box;
    padding: 0 20px;
    box-sizing: border-box;
    padding-bottom: 32px;
    :deep(.dialog-body-content) {
      color: var(--text-color-1);
      p {
        padding-left: 15px;
        padding-right: 5px;
        &.title {
          margin-top: 32px;
          margin-bottom: 20px;
          padding: 0;
          font-weight: bold;
        }
      }
      &:first-child {
        margin-top: 20px;
      }
    }
  }
}

.static-card {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 30px;
  padding: 32px 16px;

  box-sizing: border-box;
  p.title {
    color: var(--text-color-1);
    font-size: 28px;
    font-weight: 500;
    line-height: 32px; /* 114.286% */
    letter-spacing: 3.2px;
    padding-bottom: 24px;
  }
  .list {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: space-between;
    .item {
      width: 215px;
      border-radius: 20px;
      background-color: #fff;
      color: var(--text-color-1);
      font-size: 24px;
      font-weight: 500;
      line-height: 1.5;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      padding: 20px 0;
      position: relative;
      .btn-tip {
        position: absolute;
        right: 15px;
        top: 15px;
      }
      .sub {
        font-size: 20px;
        font-weight: 400;
        word-spacing: 2px;
      }
    }
  }
  .btn-link {
    width: 100%;
    height: 24;
    line-height: 24px;
    margin-top: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #3566ff;
    font-size: 20px;
    font-weight: 400;
    line-height: 24px; /* 120% */
    cursor: pointer;
    &:active {
      opacity: 0.7;
    }
    &::after {
      content: '';
      display: inline-block;
      width: 24px;
      height: 24px;
      background: url('../img/icon-arrow-down.png') no-repeat;
      background-size: 100% 100%;
      transform: rotate(-90deg);
      transform-origin: center center;
    }
  }
}

.point-card {
  width: 702px;
  height: 355px;
  cursor: pointer;
  margin: 0 auto;
  margin-top: 32px;
  img {
    width: 117px;
    height: 75px;
    margin-bottom: 12px;
  }
}

.earnings-rate-date {
  font-size: 20px;
  line-height: 40px;
  color: var(--text-color-1);
  margin-bottom: 40px;
}
.about-us {
  width: 702px;
  height: 355px;
  cursor: pointer;
  margin: 0 auto;
  margin-top: 32px;
  img {
    width: 44px;
    height: 44px;
    margin-bottom: 12px;
  }
  .item {
    padding-top: 24px;
  }
}

.companys {
  width: 702px;
  height: 778px;
  cursor: pointer;
  margin: 0 auto;
  margin-top: 32px;
  padding: 32px;
  .bank-list {
    display: grid;
    grid-template-columns: repeat(3, 33.33%);
    grid-template-rows: repeat(3, 52px);
    grid-row-gap: 16px;
    .item {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      font-size: 24px;
      line-height: 36px;
      color: var(--text-color-1);
      img {
        width: 52px;
        height: 52px;
        margin-right: 16px;
      }
    }
  }
}

.card-xiaozhishi {
  width: 702px;
  margin: 0 auto;
  margin-top: 32px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 20px;
  position: relative;
  &::after {
    content: '';
    display: inline-block;
    width: 114px;
    height: 75px;
    background: url('../img/icon-complete-study.png') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    right: 0;
    bottom: 0;
  }
  p {
    font-size: 24px;
    color: var(--text-color-1);
    letter-spacing: 0;
    line-height: 36px;
    text-align: left;
    z-index: 2;
    span {
      color: #f24c3d;
      font-weight: bold;
    }
  }
}

.warning-text {
  width: 702px;
  margin: 0 auto;
  margin-top: 32px;
  box-sizing: border-box;
  font-size: 24px;
  color: var(--text-color-1);
  letter-spacing: 0;
  line-height: 36px;
  text-align: left;

  .title {
    font-weight: bold;
    margin-bottom: 16px;
  }
}
.logo {
  width: 100%;
  height: 232px;
}

.act-data {
  width: 702px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20px;
  padding: 32px;
  padding-bottom: 40px;
  color: var(--text-color-1);
  margin: 0 auto;
  margin-bottom: 24px;
  .row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    &.line2 {
      margin-top: 32px;
    }
    .title {
      font-weight: bold;
      font-size: 26px;
      line-height: 40px;
    }
    .left,
    .right {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: 50%;
      span {
        align-self: flex-end;
        font-weight: normal;
        font-size: 20px;
        display: block;
        height: 18px;
        line-height: 0;
      }
    }
    .left {
      &.title {
        position: relative;
        &::after {
          content: '';
          width: 24px;
          height: 24px;
          background: url('../img/icon-arrow-down.png') no-repeat;
          background-size: 100% 100%;
          transform: rotate(-90deg);
        }
      }
      &.money {
        font-size: 50px;
        font-weight: bold;
        line-height: 50px;
        color: var(--prodcard-color-4);
      }
    }
    .right {
      &.point {
        font-size: 50px;
        font-weight: bold;
        line-height: 50px;
        color: var(--prodcard-color-4);
        &.is-zero {
          color: var(--text-color-2);
          font-size: 32px;
        }
      }
    }
  }

  .line2 {
    .right {
      display: flex;
      align-items: baseline;
      justify-content: flex-start;
      .base-point {
        font-size: 24px;
        color: rgba(180, 186, 204, 1);
        line-height: 24px;
        position: relative;
        font-weight: 400;
        margin-left: 18px;
        &::after {
          content: '';
          width: 100%;
          height: 1px;
          background-color: rgba(180, 186, 204, 1);
          position: absolute;
          top: 45%;
          left: 0;
        }
      }
    }
  }

  .line3 {
    font-size: 20px;
    color: rgba(165, 81, 33, 1);
    line-height: 20px;
    margin-bottom: -22px;
    .right {
      .bonus-text-box {
        padding: 10px 14px;
        background-color: #f9f2f4;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;

        &::after {
          content: '';
          width: 10px;
          height: 10px;
          background-color: #f9f2f4;
          position: absolute;
          top: -6px;
          left: 22px;
          transform: rotate(45deg);
        }
      }
    }
  }
  .line4 {
    font-size: 24px;
    color: var(--text-color-3);
    line-height: 36px;
    margin-top: 32px;
    .left {
      width: 100%;
    }
  }
}
.active-number {
  margin-top: 48px;
  color: var(--text-color-1);
  font-size: 20px;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  width: 100%;
  position: relative;
  padding: 0 32px;
  &:after,
  &::before {
    content: '';
    width: 242px;
    height: 1px;
    background-color: var(--text-color-3);
    opacity: 0.3;
  }
  &::before {
    margin-right: 18px;
    left: 0;
  }
  &::after {
    margin-left: 18px;
  }
  p {
    flex: none;
    width: fit-content;
  }
}
.btn-adding {
  width: 702px;
  height: 88px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  bottom: 24px;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 88px;
  background-color: #ffa448;
  color: #fff;
  font-size: 28px;
  z-index: 222;
  &::after {
    content: '报名后参与得奖励';
    width: 178px;
    height: 28px;
    display: block;
    position: absolute;
    right: 30px;
    top: -10px;
    color: #e07d3d;
    font-size: 20px;
    line-height: 28px;
    text-align: center;
    background-color: #ffdea6;
    border-radius: 14px 2px 14px 2px;
  }
  &.is-adding {
    background-color: #f7dbcb;
    &::after {
      display: none;
    }
  }
}
</style>
