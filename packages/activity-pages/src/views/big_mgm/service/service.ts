import Page1_1 from '@/views/time_travel/components/page-1_1.vue'
import { focusStore, focusServices, focusCore, focusUtils } from '@focus/render'
console.log('🚀 ~ file: service.ts ~ line 2 ~ focusUtils', focusUtils)
const { entitiestoUtf16, dayjs } = focusUtils
const { mgmService, kvService, dynimicDataService } = focusServices
const cgis = {
  bigMgmHome: {
    url: '/wm-htrserver/op/mgm2/home',
    name: '获取大MGM首页信息',
  },
  rankList: {
    url: '/wm-htrserver/op/mgm2/top',
    name: '获取大MGM排行榜',
  },
  bigMgmInviteList: {
    url: '/wm-htrserver/op/mgm2/list_relation_helper',
    name: '获取大MGM邀请列表',
  },
  inviteListV2: {
    url: '/op-fmfront/mgm/hj/fm/query-invite-list',
    name: '新大mgm邀请列表',
  },
}

const cgisV2 = {
  bigMgmHome: {
    url: '/op-fmfront/great/mgm/hj/fm/query-home-info',
    name: '获取大MGM首页信息',
  },

  rankList: {
    url: '/op-fmfront/great/mgm/hj/fm/query-rank-list',
    name: '获取大MGM排行榜',
  },
  pointList: {
    url: '/op-fmfront/great/mgm/hj/fm/query-point-month',
    name: '大mgm积分历史记录',
  },
  inviteList: {
    url: '/op-fmfront/great/mgm/hj/fm/query-invite-list',
    name: '新大mgm邀请列表',
  },
  hideName: {
    url: '/op-fmfront/great/mgm/hj/fm/save-rank-anonymity-status',
    name: '设置匿名',
  },
  checkHideNameStatus: {
    url: '/op-fmfront/great/mgm/hj/fm/query-rank-anonymity-status',
    name: '查询匿名状态',
  },
}

class Service {
  _activeLink: string = ''
  _inviteLink: string = ''
  constructor() {
    this._activeLink = ''
    this._inviteLink = ''
  }
  get activeLink() {
    return this._activeLink
  }
  get inviteLink() {
    return this._inviteLink
  }
  setHomeData(p: { activeLink?: string; inviteLink?: string }) {
    this._inviteLink = p.inviteLink || ''
    this._activeLink = p.activeLink || ''
  }
  queryHomeData(): Promise<{
    curPoints: number | string // 当前总积分
    lastDayNewPoints: number | string // 昨日新增积分
    curInvited: number | string // 当前邀请人数
    lastDayNewInvited: number | string // 昨日新增邀请人数
    isInvitedStatus: boolean
    bubbleData: { high_light: string[]; text: string }[]
    sid: string
    curPointsForamt: string
    isError: boolean
    noActivateInviteSum?: number
    equityLevel?: string
    equityBonus?: string
  }> {
    return new Promise((resolve, reject) => {
      focusCore
        .request(cgisV2.bigMgmHome, {
          activityId: 1939,
        })
        .then(
          (res: {
            // 活动状态：true-有效 false-无效
            activityStatu: boolean
            // 头像
            nickName: string
            // 昵称
            headImgUrl: string
            // 是否有邀请: 0-未邀请 1-已邀请
            isInvite: number
            // 邀请总人数、不含未开户
            totalInviteSum: number
            // 已开户未激活人数
            noActivateInviteSum: number
            // 昨日邀请人数、不含未开户
            lastDayInviteSum: number
            // 总积分
            totalPoint: number
            // 昨日积分
            lastDayPoint: number
            // 权益等级:1:白银,2:黄金,3:铂金,4:钻石
            equityLevel: string
            // 权益奖励系数:1:白银,1:黄金,1.1:铂金,1.2:钻石
            equityBonusTimes: string
            // 单日累计（前三）

            dayRandomList: Array<{
              userName: string
              // 真实姓名（掩码）
              inviteSum: number
              // 邀请人数
              point: number
            }>
            //月度累计（前三）
            monthRandomList: Array<{
              // 真实姓名（掩码）
              userName: string
              // 邀请人数
              inviteSum: number
              point: number
            }>
          }) => {
            const {
              isInvite,
              totalPoint = -1,
              lastDayPoint = -1,
              lastDayInviteSum = -1,
              noActivateInviteSum = -1,
              totalInviteSum = -1,
              equityBonusTimes,
              equityLevel,
            } = res
            let equityBonus = ''
            if ((equityLevel === '3' || equityLevel === '4') && equityBonusTimes) {
              equityBonus = parseInt((Number(equityBonusTimes) * 100 - 100).toString(), 10) + '%'
            }
            resolve({
              isError: false,
              isInvitedStatus: !!isInvite, // 是否邀请了人
              curPointsForamt: this.formatePoints(totalPoint),
              curPoints: lastDayPoint === -1 ? '--' : lastDayPoint,
              lastDayNewPoints: lastDayPoint === -1 ? '--' : lastDayPoint,
              curInvited: totalInviteSum === -1 ? '--' : totalInviteSum,
              lastDayNewInvited: lastDayInviteSum === -1 ? '--' : lastDayInviteSum,
              bubbleData: [],
              sid: '',
              noActivateInviteSum,
              equityLevel,
              equityBonus,
            })
          },
        )
        .catch(() => {
          return reject(false)
        })
    })
  }

  formatePoints(points: number): string {
    if (points === -1) {
      return '--'
    }
    let result: number | string = points
    if (points >= 100000000) {
      result = parseInt((points / 1000000).toString(), 10) / 100 + '亿'
    } else if (points >= 100000) {
      result = parseInt((points / 100).toString(), 10) / 100 + '万'
    }
    return result.toString()
  }

  queryMgmData(): Promise<{
    curPoints: number // 当前总积分
    lastDayNewPoints: number // 昨日新增积分
    curInvited: number // 当前邀请人数
    lastDayNewInvited: number // 昨日新增邀请人数
    isInvitedStatus: boolean
    bubbleData: { high_light: string[]; text: string }[]
    sid: string
    curPointsForamt: string
    isError: boolean
  }> {
    return new Promise((resolve) => {
      mgmService.getUserSid('1939').then((res: any) => {
        const { userSid } = res
        focusCore
          .request(cgis.bigMgmHome, {
            sid: userSid,
            activity_id: 1939,
            top: false,
          })
          .then((res: any) => {
            console.log('🚀 ~ file: service.ts:27 ~ Service ~ focusCore.request ~ res:', res)
            //判断是否有邀请态
            const {
              relation_summary = {
                total_invite_count: 0,
                last_day_invite_count: 0,
                last_day_point: 0,
                level2_last_day_invite_count: 0,
                level2_total_invite_count: 0,
                total_point: 0,
              },
              random_show = {
                summary: {
                  last_day_top: [
                    {
                      high_light: [null, '0', '25'],
                      text: 'null厉害啦！昨日共邀请0人，获得25积分！',
                    },
                  ],
                  stat: [
                    {
                      high_light: ['100%', '21'],
                      text: '大家都很优秀！据说昨日100%的用户获得积分21+',
                    },
                  ],
                  total_top: [
                    {
                      high_light: [null, '0', '100000'],
                      text: 'null太强啦！已经累计邀请0人，获得100000积分！',
                    },
                  ],
                },
              },
            } = res
            const isInvitedStatus = !!relation_summary.total_invite_count
            const bubbleData = (random_show.summary && random_show.summary.total_top) || []
            const {
              total_invite_count = 0,
              last_day_invite_count = 0,
              last_day_point = 0,
              level2_last_day_invite_count = 0,
              level2_total_invite_count = 0,
              total_point = 0,
            } = relation_summary

            let curPointsForamt = this.formatePoints(total_point)
            // const curPointsForamt =
            //   1530000 >= 100000 ? parseInt((1530000 / 100).toString(), 10) / 100 + '万' : total_point.toString()
            resolve({
              isInvitedStatus, // 是否邀请了人
              curPoints: total_point,
              // curPoints: 300,
              curPointsForamt,
              lastDayNewPoints: last_day_point,
              curInvited: total_invite_count,
              // curInvited: 20,
              lastDayNewInvited: last_day_invite_count,
              bubbleData,
              sid: userSid,
              isError: false,
            })
          })
          .catch((err: any) => {
            console.log('🚀 ~ file: service.ts:29 ~ Service ~ focusCore.request ~ err:', err)
            resolve({
              isError: true,
              isInvitedStatus: false, // 是否邀请了人
              curPointsForamt: '0',
              curPoints: 0,
              lastDayNewPoints: 0,
              curInvited: 0,
              lastDayNewInvited: 0,
              bubbleData: [],
              sid: '',
            })
          })
      })
    })
  }

  queryInviteList(): Promise<{
    active?: Array<any>
    notActive: Array<any>
    notRegister: Array<any>
    m2DepositActiveNumber?: number | string
  }> {
    return new Promise((resolve) => {
      return focusCore
        .request(cgisV2.inviteList, {
          activityId: 1939,
          notRegisterDay: 30,
        })
        .then(
          (res: {
            // 未开户列表
            notRegisterList: Array<{
              // 展示时间
              showTime: string
              // m2用户信息
              account: {
                sid: string
                nickName: string
                headImgUrl: string
                gender: string
              }
            }>

            // 已开户未激活列表
            notActiveList: Array<{
              // 展示时间
              showTime: string
              // m2用户信息
              account: {
                sid: string
                nickName: string
                headImgUrl: string
                gender: string
              }
            }>
            // 已激活列表

            activeList: Array<{
              // 展示时间
              showTime: string
              // m2用户信息
              account: {
                sid: string
                nickName: string
                headImgUrl: string
                gender: string
              }
              bonusTimes: string
            }>

            // 未开户列表总数
            notRegisterInviteSum: number
            // 已开户未激活列表总数
            notActiveInviteSum: number
            activeInviteSum: number
            m2DepositActiveNumber: number
          }) => {
            const { notRegisterList = [], notActiveList = [], activeList = [], m2DepositActiveNumber = '--' } = res
            console.log('🚀 ~ Service ~ returnnewPromise ~ res:', res)
            return resolve({
              notActive: notActiveList.map((i) => {
                let nickName = entitiestoUtf16(i.account.nickName)
                let avator = i.account.headImgUrl.replace('http:', 'https:')
                return {
                  ...i,
                  nickName,
                  avator,
                }
              }),
              notRegister: notRegisterList.map((i) => {
                const { showTime } = i
                const d: any = dayjs(showTime)
                // 当天>>今天
                // 昨天>>昨天
                // 7天内>>具体时间
                // 7天外-30天内>>7天前
                // 超30天>>不展示
                let showTimeText = showTime

                const isToday = d.isToday()
                const isYesterday = d.isYesterday()

                // 计算7天前的日期
                const now = dayjs()
                const sevenDaysAgo = now.subtract(7, 'day')
                // 检查 dateToCheck 是否在过去7天内（包括今天和7天前）
                if (d.isBetween(sevenDaysAgo, now, null, '[]')) {
                  console.log('该日期在过去7天内。')
                } else {
                  console.log('该日期不在过去7天内。')
                  showTimeText = '7天前'
                }

                if (isToday) {
                  showTimeText = '今天'
                }
                if (isYesterday) {
                  showTimeText = '昨天'
                }
                let nickName = entitiestoUtf16(i.account.nickName)
                let avator = i.account.headImgUrl.replace('http:', 'https:')
                return {
                  ...i,
                  showTime: showTimeText,
                  nickName,
                  avator,
                }
              }),
              active: activeList.map((i) => {
                let nickName = entitiestoUtf16(i.account.nickName)
                let avator = i.account.headImgUrl.replace('http:', 'https:')
                if (activeList.length <= 3) {
                  nickName = ''
                  avator = ''
                }
                return {
                  ...i,
                  nickName,
                  avator,
                }
              }),
              m2DepositActiveNumber,
            })
          },
        )
        .catch((err: any) => {
          console.log('🚀 ~ Service ~ returnnewPromise ~ err:', err)
          resolve({
            notActive: [],
            notRegister: [],
            active: [],
          })
        })
    })
  }

  queryPointList(month: string): Promise<{
    lastDayPoint: number | string
    totalPoint: number | string
    pointCount: number | string
    voList: Array<{
      day: number
      // 积分奖励月份
      month: number
      point: number
      time: string
    }>
  }> {
    return new Promise((resolve) => {
      focusCore
        .request(cgisV2.pointList, {
          activityId: 1939,
          month,
        })
        .then(
          (res: {
            // 昨日积分
            lastDayPoint: number
            // 总积分
            totalPoint: number
            // 当月已获得积分
            pointCount: number
            voList: Array<{
              // 积分奖励日期	20240904
              day: number
              // 积分奖励月份
              month: number
              point: number
            }>
          }) => {
            const { lastDayPoint = '--', totalPoint = '--', pointCount = '--', voList = [] } = res
            resolve({
              lastDayPoint,
              totalPoint,
              pointCount,
              voList: voList.map((i) => {
                return {
                  ...i,
                  time: dayjs(`${i.day}`).format('MM月DD日'),
                }
              }),
            })
          },
        )
        .catch((err: any) => {
          console.log('🚀 ~ Service ~ returnnewPromise ~ err:', err)
          return resolve({
            lastDayPoint: '--',
            totalPoint: '--',
            pointCount: '--',
            voList: [],
          })
        })
    })
  }

  queryRankList(): Promise<{
    dailyRank: number
    monthRank: number
    dailyRankList: any[]
    monthlyRankList: any[]
    rankListUpdateTime: string
    bubbleData: any[]
  }> {
    return new Promise((resolve) => {
      focusCore
        .request(cgisV2.rankList, {
          activityId: 1939,
        })
        .then(
          (res: {
            // 单日排名，-1 未上榜
            myDayRank: number
            // 月度排名，-1 未上榜
            myMonthRank: number
            // 排行榜数据统计时间
            dsDate: string
            // 单日排行榜（前10）
            dayRankList: Array<{
              // 排名
              rank: number
              // 真实姓名（掩码），匿名则展示：用户+手机号后4位
              userName: string
              // 性别：1-男 2-女
              gender: string
              // 积分
              point: number
            }>
            // 月度排行（前10）
            monthRankList: Array<{
              // 排名
              rank: number
              // 真实姓名（掩码），匿名则展示：用户+手机号后4位
              userName: string
              // 性别：1-男 2-女
              gender: string
              // 积分
              point: number
            }>
            dayRandomList: Array<{
              inviteSum: number
              point: number
              userName: string
            }>
            monthRandomList: Array<{
              inviteSum: number
              point: number
              userName: string
            }>
          }) => {
            console.log('🚀 ~ file: service.ts:140 ~ Service ~ returnnewPromise ~ res:', res)

            const {
              myDayRank,
              myMonthRank,
              dayRankList,
              monthRankList,
              dsDate,
              dayRandomList = [],
              monthRandomList = [],
            } = res

            return resolve({
              dailyRankList: dayRankList || [],
              monthlyRankList: monthRankList || [],
              dailyRank: myDayRank,
              monthRank: myMonthRank,
              rankListUpdateTime: dayjs(`${dsDate}`).format('YYYY-MM-DD'),
              bubbleData: dayRandomList
                .map((i) => {
                  const { inviteSum, point, userName } = i
                  return {
                    text: `${userName}厉害啦！昨日共邀请${inviteSum}人,获得${point}积分！`,
                    high_light: [inviteSum, point, userName],
                  }
                })
                .concat(
                  monthRandomList.map((i) => {
                    const { inviteSum, point, userName } = i

                    return {
                      text: `${userName}太强了！当月已累计邀请${inviteSum}人,获得${point}积分！`,
                      high_light: [inviteSum, point, userName],
                    }
                  }),
                ),
            })
          },
        )
        .catch((err: any) => {
          console.log('🚀 ~ file: service.ts:142 ~ Service ~ returnnewPromise ~ err:', err)
          return resolve({
            dailyRank: -1,
            monthRank: -1,
            dailyRankList: [],
            monthlyRankList: [],
            rankListUpdateTime: '--',
            bubbleData: [],
          })
        })
    })
  }

  checkHideName(): Promise<{ status: boolean }> {
    return new Promise((resolve) => {
      return focusCore
        .request(cgisV2.checkHideNameStatus, {
          activityId: 1939,
        })
        .then(
          (res: {
            // 匿名状态：true-已匿名，false-未匿名
            anonymityStatus: boolean
            // 活动状态：true-有效 false-无效
            activityStatus: boolean
          }) => {
            const { anonymityStatus } = res
            return resolve({
              status: anonymityStatus,
            })
          },
        )
    })
  }
  changeHideNameStatus(status: boolean) {
    return new Promise((resolve) => {
      return focusCore
        .request(cgisV2.hideName, {
          activityId: 1939,
          anonymityStatus: status,
        })
        .then(
          (res: {
            // 设置状态：true-成功 false-失败
            activityStatus: boolean
          }) => {
            console.log('🚀 ~ Service ~ returnnewPromise ~ res:', res)
            const { activityStatus } = res

            return resolve({
              status: activityStatus,
            })
          },
        )
    })
  }
}

export default new Service()
