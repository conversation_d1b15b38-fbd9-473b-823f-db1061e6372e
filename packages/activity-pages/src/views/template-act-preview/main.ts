BUILD_TEST &&
  SHOW_VCONSOLE &&
  import(/* webpackChunkName: "vconsole" */ 'vconsole').then((cls) => {
    const Cls = cls.default
    return new Cls()
  })
console.log('template_act_', 'SHOW_VCONSOLE', SHOW_VCONSOLE)
import { createApp } from 'vue'
import App from './App.vue'
import focusRenderPlugin, { focusCore, focusUtils } from '@focus/render'
import '@focus/render/build-lib/focus-render.css'
import { createPinia } from 'pinia'
import router from './routes'
const { UrlParser } = focusUtils
// if (BUILD_TEST) {
//   // 使用测试环境
//   focusCore.useTestMode()
// }
const parsed = new UrlParser(window.location.href)
const { aid = 0 } = parsed.query

const app = createApp(App)
const pinia = createPinia()
app.use(pinia).use(focusRenderPlugin, { $pinia: pinia }).use(router)
focusCore.waLog.setCustomPageId(`template_act_${aid}`)

app.mount('#app')
