<template>
  <div class="option-tool" :style="{ top: top + 'px' }">
    <div class="info">
      <p class="err">你正在使用预览模式，不代表实际效果！仅供内部使用，不得外泄！</p>
      <div class="btn-up" @click="handleUp">向上</div>
      <div class="btn-down" @click="handleDown">向下</div>
    </div>

    <div class="options">
      <div class="row">
        <div class="left">模拟加码状态</div>
        <div class="right">
          <div
            class="btn"
            :class="{ active: bonusStatus === '0', disabled: bonusGroupType !== 'CUST_GROUP' }"
            @click="handleClick('0')"
          >
            不在人群包
          </div>
          <div
            class="btn"
            :class="{ active: bonusStatus === '-1', disabled: bonusGroupType !== 'CUST_GROUP' }"
            @click="handleClick('-1')"
          >
            在人群包
          </div>
          <div
            class="btn"
            :class="{ active: bonusStatus === '1', disabled: bonusGroupType !== 'MEMBER_LEVEL' }"
            @click="handleClick('1')"
          >
            白银
          </div>
          <div
            class="btn"
            :class="{ active: bonusStatus === '2', disabled: bonusGroupType !== 'MEMBER_LEVEL' }"
            @click="handleClick('2')"
          >
            黄金
          </div>
          <div
            class="btn"
            :class="{ active: bonusStatus === '3', disabled: bonusGroupType !== 'MEMBER_LEVEL' }"
            @click="handleClick('3')"
          >
            铂金
          </div>
          <div
            class="btn"
            :class="{ active: bonusStatus === '4', disabled: bonusGroupType !== 'MEMBER_LEVEL' }"
            @click="handleClick('4')"
          >
            钻石
          </div>
          <div
            class="btn"
            :class="{ active: bonusStatus === '5', disabled: bonusGroupType !== 'ENTERPRISE_AUTH' }"
            @click="handleClick('5')"
          >
            企业认证
          </div>
          <div
            class="btn"
            :class="{ active: bonusStatus === '6', disabled: bonusGroupType !== 'ENTERPRISE_AUTH' }"
            @click="handleClick('6')"
          >
            企业认证
          </div>
          <div
            class="btn"
            :class="{ active: bonusStatus === '7', disabled: bonusGroupType !== 'NEW_CUST' }"
            @click="handleClick('7')"
          >
            是受邀新户
          </div>
          <div
            class="btn"
            :class="{ active: bonusStatus === '8', disabled: bonusGroupType !== 'NEW_CUST' }"
            @click="handleClick('8')"
          >
            不是受邀新户
          </div>
        </div>
      </div>
      <div class="row">
        <div class="left">模拟访问时间</div>
        <div class="right">
          <div class="btn">2025-09-03 19:00:00</div>
        </div>
      </div>
    </div>

    <div class="option2">
      <div class="btn">重新加载让模拟生效</div>
      <div class="btn">隐藏选项</div>
      <div class="btn">在App查看</div>
    </div>

    <div class="date-inputer">
      <div class="wrap">
        <input type="number" v-model="year" placeholder="年" />-<input type="number" v-model="month" placeholder="月" />
        -<input type="number" v-model="day" placeholder="日" />
        <input type="number" v-model="hour" placeholder="时" />:<input
          type="number"
          v-model="minute"
          placeholder="分"
        />:<input type="number" v-model="secend" placeholder="秒" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, watchEffect } from 'vue'
import { focusUtils } from '@focus/render'
const { UrlParser, dayjs } = focusUtils
const locationHrefPared = new UrlParser(window.location.href)
const { activityId = 0, activityUIType = '1', navColor = '', mockUserRank, previewId } = locationHrefPared.query

const wealthRank = inject('wealthRank')
const bonusGroupType = inject('bonusGroupType')

const top = ref(200)
const bonusStatus = ref('0')
const year = ref('2025')
const month = ref('09')
const day = ref('03')
const hour = ref('19')
const minute = ref('00')
const secend = ref('00')
function handleUp() {
  top.value = top.value - 200 < 0 ? 0 : top.value - 200
}

function handleDown() {
  top.value = top.value + 200 > 600 ? 600 : top.value + 200
}

function handleClick(id: string) {
  console.log('🚀 ~ handleClick ~ id:', id)
  bonusStatus.value = id
}

function reload() {
  let path = location.href
  let newPath = path.replace(/mockUserRank=[^&]+/, 'mockUserRank=' + bonusStatus.value)
  newPath = newPath.replace(/mockAccessTime=[^&]+/, 'mockAccessTime=' + dayjs().format('YYYY-MM-DD HH:mm:ss'))
  window.location.replace(newPath)
}

watchEffect(() => {
  if (wealthRank.value && bonusGroupType.value === 'MEMBER_LEVEL') {
    bonusStatus.value = wealthRank.value
  }
})
</script>

<style scoped lang="scss">
.option-tool {
  width: 100%;
  height: auto;
  background-color: rgba(0, 0, 0, 0.8);
  position: fixed;
  top: 200px;
  left: 0;
  right: 0;
  padding-top: 20px;
  padding-bottom: 20px;
  z-index: 99999;
  p.err {
    font-size: 24px;
    line-height: 1.5;
    color: red;
  }
  .options {
    width: 100%;
    background: #eee;
    .row {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 24px;
      color: red;
      height: auto;
      padding: 10px;
      .left {
        width: 200px;
      }
      .right {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        flex-wrap: wrap;
        width: 70%;
        .btn {
          width: fit-content;
          height: 50px;
          line-height: 50px;
          text-align: center;
          border-radius: 10px;
          color: black;
          background-color: #eee;
          padding: 0 20px;
          margin-right: 20px;
          margin-bottom: 20px;
          border-radius: 50px;
          border: 1px solid #da9a72;
          &:last-child {
            margin-right: 0;
          }

          &.active {
            background: #da9a72;
            color: #fff;
          }
          &.disabled {
            background: #ccc;
            color: #fff;
            display: none;
          }
        }
      }
    }
  }
}
.option2 {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 20px;
}
.btn,
.btn-up,
.btn-down {
  width: fit-content;
  height: 40px;
  padding: 0 20px;
  background: #da9a72;
  font-size: 24px;
  color: #fff;
  border-radius: 40px;
  line-height: 40px;
  flex: none;
}
.info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  p {
    flex: auto;
  }
}
.date-inputer {
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 30%;
  background-color: #fff;
  .wrap {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    input {
      width: 80px;
      height: 50px;
      line-height: 50px;
      text-align: center;
      border-radius: 10px;
      color: black;
      background-color: #eee;
      border: 0;
    }
  }
}
</style>
