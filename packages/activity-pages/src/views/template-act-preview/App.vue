<script setup lang="ts">
import { focusCore, focusStore, focusServices, Mask, focusUtils } from '@focus/render'
import Home from '../template-act/pages/home.vue'
import OptionTool from './components/OptionTool.vue'
import { ref, provide } from 'vue'

const wealthRank = ref(0)
const bonusGroupType = ref('')
provide('wealthRank', wealthRank)
provide('bonusGroupType', bonusGroupType)

function initSuccess(data: any) {
  console.log('🚀 ~ initSuccess ~ data:', data)
  wealthRank.value = data.wealthRank
  bonusGroupType.value = data.bonusGroupType
}
</script>

<template>
  <Home :useMock="true" @initSuccess="initSuccess" />
  <OptionTool></OptionTool>
</template>

<style lang="scss">
@import '../../styles/normalize.scss';
#app {
  width: 100%;
  min-height: 100vh;
  text-align: center;
  overflow-x: hidden;
  line-height: 0;
  font-size: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  overflow: hidden;
}
</style>
