import { createRouter, createWebHashHistory } from 'vue-router'
// import home from '../pages/home.vue'
// import historyWealth from '../pages/historyWealth.vue'
// import historyFund from '../pages/historyFund.vue'
const routes = [
  {
    path: '/',
    redirect: '/home',
  },
  // {
  //   path: '/home',
  //   component: home,
  //   meta: {w
  //     noNeedLogin: true,
  //   },
  // },
  // {
  //   path: '/historyWealth',
  //   component: historyWealth,
  //   meta: {
  //     noNeedLogin: true,
  //   },
  // },
  // {
  //   path: '/historyFund',
  //   component: historyFund,
  //   meta: {
  //     noNeedLogin: true,
  //   },
  // },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    return { top: 0 }
  },
})

export default router
