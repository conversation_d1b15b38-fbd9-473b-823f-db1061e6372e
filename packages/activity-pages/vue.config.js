const address = require('address')
const nodejs_argv = require('nodejs-argv')
const path = require('path')
const { NODE_ENV, VUE_APP_BUILD_MODE, VUE_APP_IMG_URL, VUE_APP_CDN_PREFIX } = process.env
const fs = require('fs')

const argv = nodejs_argv.new()
argv.option([
  ['-p', '--page', 'str=', '项目名'],
  ['-d', '--dev', 'str=', 'dev模式'],
  ['-log', '--log', 'str=', 'dev模式'],
  ['-vc', '--vconcole', 'str=', 'vconsole是否隐藏'],
  ['-hash', '--hash', 'str=', 'vconsole是否隐藏'],
])
try {
  argv.parse()
} catch (e) {
  console.log('Error:', e)
}
let pageName = ''
let showVConsole = true
let useHash = true
if (argv.get('-p')) {
  pageName = argv.get('-p')
  console.log('🚀 ~ file: vue.config.js ~ line 14 ~ pageName', pageName)
}
if (!pageName) {
  console.error('没有目录名！')
  return
}
if (argv.get('--vconsole')) {
  showVConsole = argv.get('--vconsole') !== 'hide'
  console.log('🚀 ~ 是否展示vconsle', showVConsole)
}
if (argv.get('--hash') === 'no') {
  useHash = false
  console.log('🚀 ~ useHash:', useHash)
}

const isDev = argv.get('--dev') === 'dev'
console.log('🚀 ~ file: vue.config.js ~ line 28 ~ isDev', isDev)
const showLog = isDev || argv.get('--log') === 'log'
const serverIsTest = VUE_APP_BUILD_MODE === 'test'
const loaclIp = address.ip()

console.log(`NODE_ENV=${NODE_ENV}`)
console.log(`打包的环境参数VUE_APP_BUILD_MODE=${VUE_APP_BUILD_MODE}`)
const ajaxBaseUrl = serverIsTest ? `https://personal.test.webank.com` : `https://personal.webank.com`
const pages = {
  family2: {
    // page 的入口
    // 模板来源
    entry: './src/views/family/main.ts',
    // 在 dist/index.html 的输出
    filename: './family2/index.html',
    // 当使用 title 选项时，
    // 模板来源
    template: './src/views/family/index.html',
    // 当使用 title 选项时，
    // template 中的 title 标签需要是 <title><%= htmlWebpackPlugin.options.title %></title>
    title: '我的家庭',
    // 在这个页面中包含的块，默认情况下会包含
    // 提取出来的通用 chunk 和 vendor chunk。
    chunks: ['log-resource', 'chunk-vendors', 'chunk-common', 'family2'],
  },
  seven_star: {
    // page 的入口
    // 模板来源
    entry: './src/views/seven_star/main.ts',
    // 在 dist/index.html 的输出
    filename: './seven_star/index.html',
    // 当使用 title 选项时，
    // 模板来源
    template: './src/views/seven_star/index.html',
    // 当使用 title 选项时，
    // template 中的 title 标签需要是 <title><%= htmlWebpackPlugin.options.title %></title>
    title: '微众银行',
    // 在这个页面中包含的块，默认情况下会包含
    // 提取出来的通用 chunk 和 vendor chunk。
    chunks: ['log-resource', 'chunk-vendors', 'chunk-common', 'seven_star'],
  },
}
getAllPages()
function getAllPages() {
  fs.readdirSync('./src/views').forEach((fileName) => {
    if (fileName.indexOf('.') < 0 && !pages[fileName]) {
      pages[fileName] = {
        // page 的入口
        // 模板来源
        entry: `./src/views/${fileName}/main.ts`,
        // 在 dist/index.html 的输出
        filename: `./${fileName}/index.html`,
        // 当使用 title 选项时，
        // 模板来源
        template: `./src/views/${fileName}/index.html`,
        // 当使用 title 选项时，
        // 在这个页面中包含的块，默认情况下会包含
        // 提取出来的通用 chunk 和 vendor chunk。
        chunks: ['log-resource', 'chunk-vendors', 'chunk-common', `${fileName}`],
      }
    }
  })
}
// console.log(pages)
const buildOptions = (() => {
  const targetPages = {}
  if (pageName) {
    targetPages[pageName] = pages[pageName]
  }
  return {
    // baseUrl: `https://personal${isTest}.webank.com/s/hj/op2/${pageName}`,
    targetPages: targetPages,
    outputDir: serverIsTest ? 'client-test' : `client-prod`,
    publicPath: VUE_APP_CDN_PREFIX + '/hj/focus2',
  }
})()

const { targetPages, outputDir, publicPath } = buildOptions
console.log('🚀 ~ publicPath: ', publicPath)
console.log('🚀 ~ outputDir: ', outputDir)
const cdnPrefixFocusRender = publicPath + '/focus-render'
console.log('🚀 ~ cdnPrefixFocusRender: ', cdnPrefixFocusRender)
module.exports = {
  lintOnSave: false,
  pages: targetPages,
  publicPath: isDev ? '/dev/' : publicPath,
  outputDir,
  assetsDir: isDev ? `` : `./${pageName}/`,
  // filenameHashing: VUE_APP_BUILD_MODE === 'prod' ? true : false,
  filenameHashing: useHash,
  runtimeCompiler: true,
  productionSourceMap: serverIsTest ? true : false,
  css: {
    extract: true,
    loaderOptions: {
      scss: {
        // @/ 是 src/ 的别名
        // 所以这里假设你有 `src/variables.scss` 这个文件
        prependData: `@import "./src/styles/variables.scss";@import "./src/styles/variables.${VUE_APP_BUILD_MODE}.scss";`,
        implementation: require('sass'), // This line must in sass option
      },
    },
  },
  devServer: {
    host: '127.0.0.1',
    disableHostCheck: true,
    port: 8888,
    // https: true,
    after: function (app) {
      // 使用配置的host和port代替server.address()
      const bind = `http://${this.host}:${this.port}`;
      console.log(`App running at: ${bind}/dev/${pageName}`);
    },
    proxy: {
      // '^/wm-htrserver/*': {
      //   pathRewrite: { '^/wm-htrserver': '/k/wm-htrserver' },
      //   changeOrigin: true,
      //   target: `https://personal.test.webank.com`,
      // },
      '/user/querycookie ': {
        changeOrigin: true,
        target: `${loaclIp}:3008`,
      },
      '/common/systimeinfo': {
        changeOrigin: true,
        target: `${loaclIp}:3008`,
      },
      '/welfare/queryactivityinfo': {
        changeOrigin: true,
        target: `${loaclIp}:3008`,
      },
    },
  },

  chainWebpack: (config) => {
    // 增加全局变量
    config.plugin('define').tap((args) => {
      args[0].BUILD_MODE = `"${VUE_APP_BUILD_MODE}"`
      args[0].LOCAL_IP = `"${loaclIp}"`
      args[0].IMG_URL = `"${VUE_APP_IMG_URL}"`
      args[0].CDN_PREFIX_FOCUS_RENDER = `"${cdnPrefixFocusRender}"`
      args[0].AJAX_BASE_URL = `"${ajaxBaseUrl}"`
      args[0].BUILD_TEST = serverIsTest
      args[0].SHOW_VCONSOLE = showVConsole
      return args
    })

    const oldUrlLoader = config.module.rule('images').use('url-loader').toConfig()
    console.log(typeof oldUrlLoader)
    config.module.rule('svga').merge({
      test: /\.svga$/,
      use: [
        {
          ...oldUrlLoader,
        },
      ],
    })
    // config.module.rule('svga').merge({
    //   test: /\.svga$/,
    //   use: [
    //     {
    //       ...oldUrlLoader,
    //       options: {
    //         limit: 5 * 1024,
    //         fallback: {
    //           ...oldUrlLoader.options.fallback,
    //           options: {
    //             name: `${pageName}/img/[name].[hash:8].[ext]`,
    //             publicPath: publicPath,
    //             outputPath: `./`,
    //           },
    //         },
    //       },
    //     },
    //   ],
    // })

    config.module
      .rule('images')
      .use('url-loader')
      .tap((options) => {
        options = {
          ...options,
          limit: 5 * 1024,
          fallback: {
            ...options.fallback,
            options: {
              name: `${pageName}/img/[name].[hash:8].[ext]`,
              publicPath: publicPath,
              outputPath: `./`,
            },
          },
        }
        return options
      })

    if (VUE_APP_BUILD_MODE === 'test') {
      // config.when(VUE_APP_BUILD_MODE === 'test', (config) => {
      //   config.optimization.minimizer('terser').tap((args) => {
      //     args[0].terserOptions.compress.drop_console = true
      //     if (!showLog) {
      //       console.log('移除consolelog')
      //     }
      //     return args
      //   })
      // })
      config.optimization.minimize(false)
    }
    config.plugin(`preload-${pageName}`).tap((options) => {
      options[0].include.entries.unshift('log-resource')
      return options
    })

    config.when(VUE_APP_BUILD_MODE === 'prod', (config) => {
      config.optimization.minimizer('terser').tap((args) => {
        if (!showLog) {
          console.log('移除consolelog')
          args[0].terserOptions.compress.drop_console = true
        }
        return args
      })
    })

    config.module.rule('ts').use('ts-loader')
  },
  configureWebpack: (config) => {
    if (config.entry.client) {
      config.entry = {
        'log-resource': [path.resolve(__dirname, 'src/service/focus-core/log-resource.ts')],
        ...config.entry,
      }
    }

    // 添加构建完成后打印自定义文本的插件
    config.plugins.push({
      apply: (compiler) => {
        compiler.hooks.done.tap('BuildCompletePlugin', (stats) => {
          setTimeout(() => {
            // 检测本地127.0.0.1:8899服务是否开启
            const net = require('net');
            const client = new net.Socket();
            let isServerRunning = false;

            client.connect(8899, '127.0.0.1', () => {
              isServerRunning = true;
              client.destroy();
            });

            client.on('error', () => {
              client.destroy();
            });

            client.on('close', () => {
              // 定义颜色和样式常量
              const GREEN = '\x1b[32m';
              const RED = '\x1b[31m';
              const CYAN = '\x1b[36m';
              const YELLOW = '\x1b[33m';
              const BOLD = '\x1b[1m';
              const RESET = '\x1b[0m';
              const SEPARATOR = `${GREEN}=====================================${RESET}`;

              // 组织提示信息
              const messages = [
                '',
                SEPARATOR,
                `${YELLOW}  ⚠️ 提示：新分支首次启动请先执行: pnpm run ready-test (初始化依赖和配置)${RESET}`,
                `${BOLD}${CYAN}步骤 1: 配置代理服务${RESET}`,
                `${GREEN}  作用：将线上请求转发到本地开发环境${RESET}`,
                `${GREEN}  1. 安装 whisle 插件: npm install -g whistle${RESET}`,
                `${GREEN}  2. 启动服务: w2 start${RESET}`,
                `${GREEN}  3. 添加以下代理规则:${RESET}`,
                `${GREEN}     - https://m.test.webank.com/s/hj/focus2/  http://127.0.0.1:8888/dev/`,
                `${GREEN}     - https://dbd.test.webankwealthcdn.net/wm-resm/hj/focus2/client/  http://127.0.0.1:8888/dev/`,
                '',
                isServerRunning ?
                  `${GREEN}✅ 检测到 whisle 服务 (127.0.0.1:8899) 已开启${RESET}` :
                  `${RED}❌ 检测到 whisle 服务 (127.0.0.1:8899) 未开启，请先启动服务${RESET}`,
                '',
                `${BOLD}${CYAN}步骤 2: 配置微信开发者工具网络代理${RESET}`,
                `${GREEN}  1. 打开[微信开发者工具]${RESET}`,
                `${GREEN}  2. 进入设置 > 代理设置 > 手动设置代理${RESET}`,
                `${GREEN}  3. 输入: 127.0.0.1:8899${RESET}`,
                '',
                `${BOLD}${CYAN}步骤 3: 获取访问链接${RESET}`,
                `${YELLOW}  focus管理端测试环境: ${BOLD}http://10.107.97.66/focusv3/#/${RESET}`,
                `${GREEN}  从管理端获取有效的fid参数${RESET}`,
                '',
                `${BOLD}${CYAN}步骤 4: 在微信开发者工具中访问${RESET}`,
                `${GREEN}  使用以下格式的地址:${RESET}`,
                `${BOLD}${GREEN}  https://m.test.webank.com/s/hj/focus2/${pageName}/index.html?fid=xxx${RESET}`,
                `${GREEN}  (将xxx替换为从管理端获取的实际fid值)${RESET}`,
                SEPARATOR,
                ''
              ];

              // 一次性打印所有消息
              console.log(messages.join('\n'));
            });
          }, 100);
        });
      }
    });
  },




}
