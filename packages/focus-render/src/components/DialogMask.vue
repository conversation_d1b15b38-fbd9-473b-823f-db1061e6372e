<template>
  <div class="dialog-mask">
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  useSlots,
  UnwrapNestedRefs,
  toRefs,
  h,
  render,
  unref,
  computed,
  RendererNode,
  VNode,
  onUnmounted,
  onBeforeMount,
  onBeforeUpdate,
  onBeforeUnmount,
  watch,
  Slot,
  withDirectives,
  ssrContextKey,
  watchEffect,
} from 'vue'
import useScreenSize from '@/hooks/useScreenSize'
import Mask from './Mask.vue'
const slots = useSlots()
const props = defineProps({
  show: {
    require: true,
    type: Boolean,
    default: false,
  },
  justifyContent: {
    require: false,
    type: String,
    default: 'center',
  },
  canScroll: {
    require: false,
    default: '',
  },
})

const dialogEl: any = ref('')
const { show, justifyContent, canScroll } = toRefs(props)
const { screenWidth, screenHeight } = useScreenSize()
const slotDom = ref<HTMLElement | RendererNode | null>(null)
let curBoxScale = ref(1)
watch(
  show,
  (val) => {
    if (val) {
      renderDialog()
    } else {
      console.log(dialogEl.value)
      if (dialogEl.value) {
        dialogEl.value.className = dialogEl.value.className + ' hide'

        setTimeout(() => {
          document.body.removeChild(dialogEl.value)
          dialogEl.value = ''
        }, 300)
      }
    }
  },
  {
    deep: true,
  },
)

watchEffect(() => {
  if (screenHeight.value && screenWidth.value && slotDom.value) {
    console.log('🚀 ~ watchEffect ~ screenWidth.value:', screenWidth.value)
    console.log('🚀 ~ watchEffect ~ screenHeight.value:', screenHeight.value)

    const box = slotDom.value.getBoundingClientRect()
    console.log(box)
    console.log('box.width...', box.width)
    console.log('box.height...', box.height)
    const trueBoxHeight = box.height / curBoxScale.value
    console.log('🚀 ~ watchEffect ~ trueBoxHeight:', trueBoxHeight)
    console.log('🚀 ~ trueBoxHeight > screenHeight.value * 0.8:', trueBoxHeight > screenHeight.value * 0.8)
    if (trueBoxHeight > screenHeight.value * 0.8) {
      const scale = (screenHeight.value * 0.7) / trueBoxHeight
      curBoxScale.value = scale <= 0.5 ? 0.5 : scale //
      slotDom.value.style = `transform:scale(${curBoxScale.value}) !important;`
    } else {
      slotDom.value.style.transform = ''
    }
  }
})

onBeforeMount(() => {
  console.log('onBeforeMount.........')
})

onBeforeUpdate(() => {
  console.log('onBeforeUpdate.........')
})

onBeforeUnmount(() => {
  console.log('onBeforeUnmount.........')
})

onUnmounted(() => {
  console.log('onUnmounted.........')
  // document.body.removeChild(el)
})

function renderDialog() {
  dialogEl.value = document.createElement('div')
  dialogEl.value.setAttribute('class', 'dialog-render-wrap')
  document.body.appendChild(dialogEl.value)
  console.log('slots', slots)
  const domContain: Slot | undefined = slots.dialog_contain
  console.log('🚀 ~ file: DialogMask.vue ~ line 23 ~ renderDialog ~ domContain', domContain)
  if (domContain) {
    const vNode = domContain()
    const animiateClass = {
      'flex-end': ['focus-flip-in'],
      center: ['focus-zoom-in'],
    }
    const containBox = h(vNode[0], {
      class: animiateClass[justifyContent.value],
      onVnodeMounted(vnode) {
        console.log('🚀 ~ onVnodeMounted ~ vnode:', vnode)
        console.log('DOM 已挂载', vnode.el)
        // slotDom.value = vnode.el
        console.log('🚀 ~ onVnodeMounted ~ slotDom.value:', slotDom.value)
        if (justifyContent.value === 'center') {
          vnode.el?.addEventListener('animationend', (event) => {
            slotDom.value = vnode.el
          })
        }
      },
    })
    let dialogNode = h(Mask, { justifyContent: justifyContent.value, canScroll: canScroll.value }, containBox)

    render(dialogNode, dialogEl.value)
  }
}
</script>

<style lang="scss">
.focus-flip-in {
  transform: translateY(100%);
  animation: focus-flip-in 0.3s 0.1s ease forwards;
}

.focus-zoom-in {
  opacity: 0;
  transform: scale(0);
  animation: focus-zoom-in 0.3s 0.1s ease forwards;
}

.hide {
  .focus-flip-in {
    animation: focus-flip-out 0.3s 0s ease forwards;
  }

  .focus-zoom-in {
    animation: focus-zoom-out 0.3s 0s ease forwards;
  }
}

// 从下方滑动出现
@keyframes focus-flip-in {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
// 从下方滑动消失
@keyframes focus-flip-out {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}

// 从中间放大出现
@keyframes focus-zoom-in {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  70% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes focus-zoom-out {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(0);
  }
}
.contain-wrap-default {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: visible;
}
</style>

<style lang="scss" scoped>
.dialog-mask {
  display: none;
}
</style>
