{"name": "focus", "version": "2.25.0923", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "clean": "rm -rf node_modules **/*/node_modules", "preinstall": "node ./scripts/preinstall.js", "update-version": "node ./scripts/update-version.js", "i-all": "pnpm recursive install", "ready-test": "pnpm run update-version && pnpm i --filter @focus/render && pnpm run build:test-lib --filter @focus/render && pnpm i --filter @focus/activity ", "ready-prod": "pnpm run update-version && pnpm i --filter @focus/render && pnpm run build:prod-lib --filter @focus/render && pnpm i --filter @focus/activity ", "activity-prod": "pnpm run update-version && node ./packages/activity-pages/pace-ci-prod.js", "activity-test": "pnpm run update-version && node ./packages/activity-pages/pace-ci-test.js", "lint": "eslint \"packages/**/*.{js,vue,ts}\"", "devactivity": "pnpm run dev --filter @focus/activity -p"}, "author": "y<PERSON><PERSON><PERSON>", "license": "ISC", "devDependencies": {"@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vue/cli-plugin-eslint": "~4", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^7.0.0", "eslint": "^6.8.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^7.20.0", "prettier": "^2.7.1"}, "dependencies": {}}